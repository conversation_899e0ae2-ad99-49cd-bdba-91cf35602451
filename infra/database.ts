// export const vehicleTable = new sst.aws.Dynamo("VehicleInventory", {
// 	fields: {
// 		VehicleID: "string",
// 		CreatedAt: "number",
// 		Make: "string",
// 		Model: "string",
// 		VehicleCondition: "string",
// 		VehicleStatus: "string",
// 		Price: "number",
// 		Year: "number",
// 		Vin: "string",
// 		LastSoldDate: "number",
// 		IsAvailable: "string",
// 	},
// 	primaryIndex: { hashKey: "VehicleID", rangeKey: "Vin" },
// 	globalIndexes: {
// 		PriceIndex: { hashKey: "VehicleID", rangeKey: "Price" },
// 		MakeModelIndex: { hashKey: "Make", rangeKey: "Model" },
// 		ConditionStatusIndex: {
// 			hashKey: "VehicleCondition",
// 			rangeKey: "VehicleStatus",
// 		},
// 		CreatedAtIndex: { hashKey: "VehicleID", rangeKey: "CreatedAt" },
// 		ModelYearIndex: { hashKey: "Model", rangeKey: "Year" },
// 		ConditionYearIndex: { hashKey: "VehicleCondition", rangeKey: "Year" },
// 		AvailabilityIndex: { hashKey: "IsAvailable", rangeKey: "LastSoldDate" },
// 	},
// });

// export const salesTable = new sst.aws.Dynamo("SalesWorkflow", {
// 	fields: {
// 		SaleID: "string",
// 		VehicleID: "string",
// 		CustomerID: "string",
// 	},
// 	primaryIndex: { hashKey: "SaleID", rangeKey: "CustomerID" },
// 	globalIndexes: {
// 		VehicleIndex: { hashKey: "SaleID", rangeKey: "VehicleID" },
// 	},
// });

// export const customersTable = new sst.aws.Dynamo("CustomerDetails", {
// 	fields: {
// 		CustomerID: "string",
// 		Name: "string",
// 		Email: "string",
// 		Phone: "string",
// 		CreatedAt: "number",
// 	},
// 	primaryIndex: { hashKey: "CustomerID", rangeKey: "Name" },
// 	globalIndexes: {
// 		EmailIndex: { hashKey: "Email" },
// 		PhoneIndex: { hashKey: "Phone" },
// 		CustomerAddedIndex: { hashKey: "CreatedAt" },
// 	},
// });

// export const serviceHistoryTable = new sst.aws.Dynamo("ServiceHistory", {
// 	fields: {
// 		ServiceID: "string",
// 		VehicleID: "string",
// 		CustomerID: "string",
// 		SaleID: "string",
// 		ServiceDate: "number",
// 		ServiceType: "string",
// 		Cost: "number",
// 	},
// 	primaryIndex: { hashKey: "ServiceID" },
// 	globalIndexes: {
// 		VehicleServiceIndex: { hashKey: "VehicleID", rangeKey: "ServiceDate" },
// 		CustomerServiceIndex: { hashKey: "CustomerID", rangeKey: "ServiceDate" },
// 		SaleServiceIndex: { hashKey: "SaleID", rangeKey: "ServiceDate" },
// 		ServiceCostIndex: { hashKey: "ServiceID", rangeKey: "Cost" },
// 		ServiceTypeIndex: { hashKey: "ServiceID", rangeKey: "ServiceType" },
// 	},
// });

export const DMSCoreTable = new sst.aws.Dynamo("DMSCoreData", {
	fields: {
		PK: "string",
		SK: "string",
		et: "string",
		GSI1PK: "string", //  For Parts: SUPPLIER#NAME; For Vehicles: MAKE#NAME
		// GSI1SK: "string", // For Parts: PART#ID; For Vehicles: VEHICLE#ID
		GSI2SK: "string", // For Vehicles and Parts: PRICE#NUMBER
	},
	primaryIndex: { hashKey: "PK", rangeKey: "SK" },
	globalIndexes: {
		ETI: { hashKey: "et" },
		GSI1: { hashKey: "GSI1PK", rangeKey: "PK" },
		GSI2: { hashKey: "GSI1PK", rangeKey: "GSI2SK" },
	},
});
