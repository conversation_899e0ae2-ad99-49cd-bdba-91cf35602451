import SupplierForm from "@/components/forms/SupplierForm";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogHeader,
	DialogTitle,
} from "@/components/ui/dialog";
import type { SupplierType } from "@/data/parts-schema-ui";

interface AddSupplierDialogProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	onSupplierAdded?: (supplier: SupplierType) => void;
}

export default function AddSupplierDialog({
	open,
	onOpenChange,
	onSupplierAdded,
}: AddSupplierDialogProps) {
	const handleSupplierSubmit = (data: SupplierType) => {
		if (onSupplierAdded) {
			onSupplierAdded(data);
		}
		onOpenChange(false);
	};

	const handleCancel = () => {
		onOpenChange(false);
	};

	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent className="sm:max-w-[500px] max-h-[90vh] overflow-y-auto">
				<DialogHeader>
					<DialogTitle>Add New Supplier</DialogTitle>
					<DialogDescription>
						Enter the details of the new supplier. Click save when you're done.
					</DialogDescription>
				</DialogHeader>
				<SupplierForm
					onSubmitSuccess={handleSupplierSubmit}
					onCancel={handleCancel}
				/>
			</DialogContent>
		</Dialog>
	);
}
