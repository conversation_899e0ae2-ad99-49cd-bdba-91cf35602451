import AddSupplierDialog from "@/components/dialogs/AddSupplierDialog";
import { partsFormOptions } from "@/core-utils/formOptionUtils";
import {
	type PartsCategoryType,
	type PartsStatusType,
	type SupplierType,
	categoryDisplayNames,
	categoryToUnits,
	statusDisplayNames,
} from "@/data/parts-schema-ui";
import { cn } from "@/lib/utils";
import { useForm } from "@tanstack/react-form";
import { format } from "date-fns";
import { CalendarIcon, Loader2, PlusCircle } from "lucide-react";
import { useState } from "react";
import { Button } from "../ui/button";
import { Calendar } from "../ui/calendar";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { Popover, PopoverContent, PopoverTrigger } from "../ui/popover";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectSeparator,
	SelectTrigger,
	SelectValue,
} from "../ui/select";
import { Textarea } from "../ui/textarea";
import { queryKeys, useApiQuery } from "@/core-utils/queryHooks";

export default function PartsForm() {
	const [availableUnits, setAvailableUnits] = useState<string[]>([]);
	const [isAddSupplierDialogOpen, setIsAddSupplierDialogOpen] = useState(false);
	const [suppliers, setSuppliers] = useState<
		Array<{ id: string; name: string }>
	>([]);

	const { data: suppliersData, isLoading: areSuppliersLoading } = useApiQuery(
		queryKeys.list("suppliers"),
		"/api/suppliers",
	);

	const partsForm = useForm({
		...partsFormOptions,
		onSubmit: async ({ value }) => {
			console.log(value);
		},
	});

	const handleFormReset = () => {
		console.log("resetting form");
		partsForm.reset();
	};

	const handleSupplierAdded = (supplier: SupplierType) => {
		// In a real application, this would come from the API with a real ID
		const newSupplierId = `supplier-${Date.now()}`;
		const newSupplier = {
			id: newSupplierId,
			name: supplier.name,
		};

		// Add the new supplier to the list
		setSuppliers((prev) => [...prev, newSupplier]);

		// Set the supplier in the form
		const supplierField = partsForm.getFieldMeta("supplier");
		if (supplierField) {
			partsForm.setFieldValue("supplier", newSupplierId);
		}
	};
	return (
		<div className="w-full">
			<form
				onSubmit={(e) => {
					e.preventDefault();
					e.stopPropagation();
					partsForm.handleSubmit();
				}}
				className="space-y-8"
			>
				<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
					{/* Basic info section */}
					<div className="space-y-4 md:col-span-2">
						<h3 className="text-lg font-medium">Basic Information</h3>
						<partsForm.Field name="name">
							{(field) => (
								<div className="space-y-2">
									<Label htmlFor={field.name}>Part Name</Label>
									<Input
										id={field.name}
										name={field.name}
										value={field.state.value ?? ""}
										type="text"
										onChange={(e) => field.handleChange(e.target.value)}
									/>
									{field.state.meta.errors ? (
										<em>{field.state.meta.errors.join(", ")}</em>
									) : null}
								</div>
							)}
						</partsForm.Field>
						<partsForm.Field name="sku">
							{(field) => (
								<div className="space-y-2">
									<Label htmlFor={field.name}>SKU</Label>
									<div>
										<Input
											id={field.name}
											name={field.name}
											value={field.state.value ?? ""}
											onChange={(e) => field.handleChange(e.target.value)}
										/>
										<small className="text-xs text-gray-700">
											Stock Keeping Unit - unique identifier for this Part
										</small>
									</div>
									{field.state.meta.errors ? (
										<em>{field.state.meta.errors.join(", ")}</em>
									) : null}
								</div>
							)}
						</partsForm.Field>
						<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
							<partsForm.Field
								name="category"
								listeners={{
									onChange: ({ value }) => {
										if (value) {
											const units = categoryToUnits[value] || [];
											setAvailableUnits(units);

											// If current unit is not valid for this category, set to first available
											const currentUnit = partsForm.getFieldValue("unit");
											if (!units.includes(currentUnit) && units.length > 0) {
												partsForm.setFieldValue("unit", units[0]);
											}
										}
									},
								}}
							>
								{(field) => (
									<div className="space-y-2">
										<Label>Category</Label>
										<Select
											name={field.name}
											onValueChange={(e) =>
												field.handleChange(e as PartsCategoryType)
											}
											defaultValue={field.state.value}
										>
											<SelectTrigger className="w-full">
												<SelectValue placeholder="Select a category" />
											</SelectTrigger>
											<SelectContent>
												{Object.entries(categoryDisplayNames).map(
													([value, label]) => (
														<SelectItem key={value} value={value}>
															{label}
														</SelectItem>
													),
												)}
											</SelectContent>
										</Select>
									</div>
								)}
							</partsForm.Field>
							<partsForm.Field name="status">
								{(field) => (
									<div className="space-y-2">
										<Label>Status</Label>
										<Select
											name={field.name}
											onValueChange={(e) =>
												field.handleChange(e as PartsStatusType)
											}
											defaultValue={field.state.value}
										>
											<SelectTrigger className="w-full">
												<SelectValue placeholder="Select status" />
											</SelectTrigger>
											<SelectContent>
												{Object.entries(statusDisplayNames).map(
													([value, label]) => (
														<SelectItem key={value} value={value}>
															{label}
														</SelectItem>
													),
												)}
											</SelectContent>
										</Select>
									</div>
								)}
							</partsForm.Field>
						</div>
					</div>

					{/* Inventory section */}
					<div className="space-y-4 md:col-span-2">
						<h3 className="text-lg font-medium">Inventory Details</h3>
						<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
							<partsForm.Field name="quantity">
								{(field) => (
									<div className="space-y-2">
										<Label htmlFor={field.name}>Quantity</Label>
										<Input
											type="number"
											id={field.name}
											name={field.name}
											value={field.state.value ?? 0}
											onChange={(e) =>
												field.handleChange(e.target.valueAsNumber)
											}
										/>
									</div>
								)}
							</partsForm.Field>
							<partsForm.Field name="minQuantity">
								{(field) => (
									<div className="space-y-2">
										<Label htmlFor={field.name}>Minimum Quantity</Label>
										<div>
											<Input
												type="number"
												id={field.name}
												name={field.name}
												value={field.state.value ?? 0}
												onChange={(e) =>
													field.handleChange(e.target.valueAsNumber)
												}
											/>
											<small>Reorder when below this level</small>
										</div>
									</div>
								)}
							</partsForm.Field>
							<div className="grid grid-cols-2 gap-2">
								<partsForm.Field name="costPerUnit">
									{(field) => (
										<div className="space-y-2">
											<Label htmlFor={field.name}>Cost Per Unit</Label>
											<Input
												type="number"
												min="0.01"
												step="0.01"
												placeholder="0.00"
												id={field.name}
												name={field.name}
												value={field.state.value ?? 0}
												onChange={(e) =>
													field.handleChange(e.target.valueAsNumber)
												}
											/>
										</div>
									)}
								</partsForm.Field>
								<partsForm.Field name="unit">
									{(field) => (
										<div className="space-y-2">
											<Label>Unit</Label>
											<Select
												name={field.name}
												onValueChange={(e) => field.handleChange(e)}
												defaultValue={field.state.value}
											>
												<SelectTrigger className="w-full">
													<SelectValue placeholder="Select unit" />
												</SelectTrigger>
												<SelectContent>
													{availableUnits.map((unit) => (
														<SelectItem key={unit} value={unit}>
															{unit}
														</SelectItem>
													))}
												</SelectContent>
											</Select>
										</div>
									)}
								</partsForm.Field>
							</div>
						</div>
					</div>

					{/* Additional Info section */}
					<div className="space-y-4 md:col-span-2">
						<h3 className="text-lg font-medium">Additional Information</h3>
						<partsForm.Field name="supplier">
							{(field) => (
								<div className="space-y-2">
									<Label>Supplier</Label>
									<div>
										<Select
											onValueChange={(value) => {
												if (value === "add-new") {
													setIsAddSupplierDialogOpen(true);
													return;
												}
												field.handleChange(value);
											}}
											defaultValue={field.state.value}
										>
											<SelectTrigger className="w-full">
												<SelectValue placeholder="Select a supplier" />
											</SelectTrigger>
											<SelectContent>
												<SelectItem value="none">None</SelectItem>
												{suppliers.map((supplier) => (
													<SelectItem key={supplier.id} value={supplier.id}>
														{supplier.name}
													</SelectItem>
												))}
												<SelectSeparator />
												<SelectItem value="add-new">
													<div className="flex items-center">
														<PlusCircle className="mr-2 h-4 w-4" />
														<span>Add New Supplier</span>
													</div>
												</SelectItem>
											</SelectContent>
										</Select>
										<small>
											Select the supplier for this part or add a new one
										</small>
									</div>
								</div>
							)}
						</partsForm.Field>

						<partsForm.Field name="warranty">
							{(field) => (
								<div className="space-y-2">
									<Label htmlFor={field.name}>Warranty</Label>
									<div>
										<Input
											id={field.name}
											name={field.name}
											value={field.state.value ?? ""}
											onChange={(e) => field.handleChange(e.target.value)}
											placeholder="e.g., 1 year limited warranty"
										/>
										<small>Optional - Enter warranty information</small>
									</div>
								</div>
							)}
						</partsForm.Field>

						<partsForm.Field name="createdAt">
							{(field) => (
								<div className="space-y-2">
									<Label>Date Added</Label>
									<Popover>
										<PopoverTrigger asChild>
											<Button
												variant={"outline"}
												className={cn(
													"w-full pl-3 text-left font-normal",
													!field.state.value && "text-muted-foreground",
												)}
											>
												{field.state.value ? (
													format(field.state.value, "PPP")
												) : (
													<span>Pick a date</span>
												)}
												<CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
											</Button>
										</PopoverTrigger>
										<PopoverContent className="w-auto p-0" align="start">
											<Calendar
												mode="single"
												selected={field.state.value}
												onSelect={field.handleChange}
												initialFocus
											/>
										</PopoverContent>
									</Popover>
								</div>
							)}
						</partsForm.Field>

						<partsForm.Field name="notes">
							{(field) => (
								<div className="space-y-2">
									<Label htmlFor={field.name}>Notes</Label>
									<div>
										<Textarea
											id={field.name}
											name={field.name}
											value={field.state.value ?? ""}
											onChange={(e) => field.handleChange(e.target.value)}
											placeholder="Additional information about this part"
											className="resize-none"
										/>
										<small>
											Optional - Add any relevant notes about this part
										</small>
									</div>
								</div>
							)}
						</partsForm.Field>
					</div>
				</div>

				<div className="flex justify-end space-x-4">
					<Button type="button" variant="outline" onClick={handleFormReset}>
						Reset
					</Button>
					<partsForm.Subscribe
						selector={(state) => [state.canSubmit, state.isSubmitting]}
						// biome-ignore lint/correctness/noChildrenProp: <explanation>
						children={([canSubmit, isSubmitting]) => (
							<Button type="submit" disabled={!canSubmit}>
								{isSubmitting ? (
									<Loader2 className="animate-spin" />
								) : (
									<span>Add Part</span>
								)}
							</Button>
						)}
					/>
				</div>
			</form>
			<AddSupplierDialog
				open={isAddSupplierDialogOpen}
				onOpenChange={setIsAddSupplierDialogOpen}
				onSupplierAdded={handleSupplierAdded}
			/>
		</div>
	);
}
