import { supplierFormOptions } from "@/core-utils/formOptionUtils";
import type { SupplierType } from "@/data/parts-schema-ui";
import { useForm } from "@tanstack/react-form";
import { Loader2 } from "lucide-react";
import { But<PERSON> } from "../ui/button";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { Textarea } from "../ui/textarea";
import { queryKeys, useApiMutation } from "@/core-utils/queryHooks";
import ApiUrlDebug from "../debug/ApiUrlDebug";
import { mutationKeys } from "../../core-utils/queryHooks";

interface SupplierFormProps {
	onSubmitSuccess?: (data: SupplierType) => void;
	onCancel?: () => void;
}

export default function SupplierForm({
	onSubmitSuccess,
	onCancel,
}: SupplierFormProps) {
	// update supplier mutation with useApiPatchMutation
	// Note: The server is already configured with .basePath("/api"), so we don't need to include "/api" in the endpoint
	const { mutateAsync: updateSupplier, isPending: isUpdatingSupplier } =
		useApiMutation("/api/suppliers", {
			mutationKey: mutationKeys.create("supplier"),
			invalidateQueries: [queryKeys.list("suppliers")],
		});

	const supplierForm = useForm({
		...supplierFormOptions,
		onSubmit: async ({ value }) => {
			try {
				// Here you would typically make an API call to save the supplier
				console.log("Submitting supplier:", value);
				console.log("API URL:", import.meta.env.VITE_API_URL);
				const result = await updateSupplier(value);
				console.log("Supplier added successfully:", result);

				if (onSubmitSuccess) {
					onSubmitSuccess(value);
				}
			} catch (error) {
				console.error("Error submitting supplier form:", error);
				console.error("Mutation error:", error);
				throw error;
			} finally {
				console.log("Form submission completed");
			}
		},
	});

	const handleFormReset = () => {
		supplierForm.reset();
		if (onCancel) {
			onCancel();
		}
	};

	return (
		<div className="w-full">
			<ApiUrlDebug />
			<form
				onSubmit={(e) => {
					e.preventDefault();
					e.stopPropagation();
					supplierForm.handleSubmit();
				}}
				className="space-y-6"
			>
				<div className="grid grid-cols-1 sm:grid-cols-2 gap-4 gap-y-5">
					<supplierForm.Field name="name">
						{(field) => (
							<div className="space-y-2">
								<Label htmlFor={field.name}>Supplier Name</Label>
								<Input
									id={field.name}
									name={field.name}
									value={field.state.value ?? ""}
									onChange={(e) => field.handleChange(e.target.value)}
								/>
								{field.state.meta.errors ? (
									<p className="text-red-500 text-xs">
										{field.state.meta.errors.join(", ")}
									</p>
								) : null}
							</div>
						)}
					</supplierForm.Field>

					<supplierForm.Field name="contactName">
						{(field) => (
							<div className="space-y-2">
								<Label htmlFor={field.name}>Contact Person</Label>
								<Input
									id={field.name}
									name={field.name}
									value={field.state.value ?? ""}
									onChange={(e) => field.handleChange(e.target.value)}
								/>
								{field.state.meta.errors ? (
									<p className="text-red-500 text-xs">
										{field.state.meta.errors.join(", ")}
									</p>
								) : null}
							</div>
						)}
					</supplierForm.Field>

					<supplierForm.Field name="email">
						{(field) => (
							<div className="space-y-2">
								<Label htmlFor={field.name}>Email</Label>
								<Input
									id={field.name}
									name={field.name}
									type="email"
									value={field.state.value ?? ""}
									onChange={(e) => field.handleChange(e.target.value)}
								/>
								{field.state.meta.errors ? (
									<p className="text-red-500 text-xs">
										{field.state.meta.errors.join(", ")}
									</p>
								) : null}
							</div>
						)}
					</supplierForm.Field>

					<supplierForm.Field name="phone">
						{(field) => (
							<div className="space-y-2">
								<Label htmlFor={field.name}>Phone</Label>
								<Input
									id={field.name}
									name={field.name}
									value={field.state.value ?? ""}
									onChange={(e) => field.handleChange(e.target.value)}
								/>
								{field.state.meta.errors ? (
									<p className="text-red-500 text-xs">
										{field.state.meta.errors.join(", ")}
									</p>
								) : null}
							</div>
						)}
					</supplierForm.Field>

					<supplierForm.Field name="address">
						{(field) => (
							<div className="space-y-2 md:col-span-2">
								<Label htmlFor={field.name}>Address</Label>
								<Input
									id={field.name}
									name={field.name}
									value={field.state.value ?? ""}
									onChange={(e) => field.handleChange(e.target.value)}
								/>
								{field.state.meta.errors ? (
									<p className="text-red-500 text-xs">
										{field.state.meta.errors.join(", ")}
									</p>
								) : null}
							</div>
						)}
					</supplierForm.Field>

					<supplierForm.Field name="website">
						{(field) => (
							<div className="space-y-2 sm:col-span-2">
								<Label htmlFor={field.name}>Website</Label>
								<Input
									id={field.name}
									name={field.name}
									type="url"
									placeholder="https://example.com"
									value={field.state.value ?? ""}
									onChange={(e) => field.handleChange(e.target.value)}
								/>
								{field.state.meta.errors ? (
									<p className="text-red-500 text-xs">
										{field.state.meta.errors.join(", ")}
									</p>
								) : null}
							</div>
						)}
					</supplierForm.Field>

					<supplierForm.Field name="notes">
						{(field) => (
							<div className="space-y-2 md:col-span-2">
								<Label htmlFor={field.name}>Notes</Label>
								<Textarea
									id={field.name}
									name={field.name}
									value={field.state.value ?? ""}
									onChange={(e) => field.handleChange(e.target.value)}
									rows={3}
								/>
								{field.state.meta.errors ? (
									<p className="text-red-500 text-xs">
										{field.state.meta.errors.join(", ")}
									</p>
								) : null}
							</div>
						)}
					</supplierForm.Field>
				</div>

				<div className="flex flex-col-reverse sm:flex-row justify-end gap-3 sm:space-x-4 mt-6">
					<Button
						type="button"
						variant="outline"
						onClick={handleFormReset}
						className="w-full sm:w-auto mb-2 sm:mb-0"
					>
						Cancel
					</Button>
					<supplierForm.Subscribe
						selector={(state) => [state.canSubmit, state.isSubmitting]}
						// biome-ignore lint/correctness/noChildrenProp: <explanation>
						children={([canSubmit, isSubmitting]) => (
							<Button
								type="submit"
								disabled={!canSubmit}
								className="w-full sm:w-auto"
							>
								{isSubmitting || isUpdatingSupplier ? (
									<>
										<Loader2 className="mr-2 h-4 w-4 animate-spin" />
										Saving...
									</>
								) : (
									"Add Supplier"
								)}
							</Button>
						)}
					/>
				</div>
			</form>
		</div>
	);
}
