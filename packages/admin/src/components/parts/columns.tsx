import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { cn } from "@/lib/utils";
import type { ColumnDef } from "@tanstack/react-table";

export type ProductData = {
	id: string;
	name: string;
	image: string;
	status: "active" | "sold_out" | "low_stock";
	stock: number;
	category: string;
	stores: number;
};

export const columns: ColumnDef<ProductData>[] = [
	{
		id: "select",
		header: ({ table }) => (
			<Checkbox
				checked={
					table.getIsAllPageRowsSelected() ||
					(table.getIsSomePageRowsSelected() && "indeterminate")
				}
				onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
				aria-label="Select all"
				className="border-dms-300"
			/>
		),
		cell: ({ row }) => (
			<Checkbox
				checked={row.getIsSelected()}
				onCheckedChange={(value) => row.toggleSelected(!!value)}
				aria-label="Select row"
				className="border-dms-300"
			/>
		),
		enableSorting: false,
		enableHiding: false,
	},
	{
		accessorKey: "image",
		header: "",
		cell: ({ row }) => {
			const image = row.getValue("image") as string;
			return (
				<div className="w-[64px] h-[20px] rounded overflow-hidden">
					<img
						src={image || "https://placehold.co/64x30"}
						alt={row.getValue("name")}
						width={64}
						height={64}
						className="object-cover w-full h-full"
					/>
				</div>
			);
		},
	},
	{
		accessorKey: "name",
		header: "Part Name",
		cell: ({ row }) => {
			const name = row.getValue("name") as string;
			const status = row.getValue("status") as string;

			return (
				<div
					className={cn(
						"font-medium",
						status === "sold_out" && "text-neutral-400",
					)}
				>
					{name}
				</div>
			);
		},
	},
	{
		accessorKey: "status",
		header: "Status",
		cell: ({ row }) => {
			const status = row.getValue("status") as string;

			return (
				<Badge
					className={cn(
						"rounded-full px-4 py-1 font-normal text-sm",
						status === "active" && "bg-dms-200 text-dms-700 hover:bg-dms-300",
						status === "sold_out" &&
							"bg-gray-200 text-gray-500 hover:bg-gray-400",
						status === "low_stock" &&
							"bg-slate-200 text-slate-900 hover:bg-slate-400",
					)}
				>
					{status === "active" && "Active"}
					{status === "sold_out" && "Sold out"}
					{status === "low_stock" && "Low in stock"}
				</Badge>
			);
		},
	},
	{
		accessorKey: "stock",
		header: "Stock info",
		cell: ({ row }) => {
			const stock = row.getValue("stock") as number;
			const status = row.getValue("status") as string;

			return (
				<div className={cn(status === "sold_out" && "text-neutral-400")}>
					{stock > 0 ? `${stock} in stock` : "0 in stock"}
				</div>
			);
		},
	},
	{
		accessorKey: "category",
		header: "Category",
		cell: ({ row }) => {
			const category = row.getValue("category") as string;
			const status = row.getValue("status") as string;

			return (
				<div className={cn(status === "sold_out" && "text-neutral-400")}>
					{category}
				</div>
			);
		},
	},
	{
		accessorKey: "stores",
		header: "Location",
		cell: ({ row }) => {
			const stores = row.getValue("stores") as number;
			const status = row.getValue("status") as string;

			return (
				<div className={cn(status === "sold_out" && "text-neutral-400")}>
					{stores === 1 ? "1 store" : `${stores} stores`}
				</div>
			);
		},
	},
];
