import type { ReactNode } from "@tanstack/react-router";
import clsx from "clsx";
import { ArrowDown, ArrowUp } from "lucide-react";

interface SummaryCardsProps {
	title: string;
	value: number | string;
	trendingUp: boolean;
	trendingValue: number;
	icon: ReactNode;
}

export default function SummaryCards({
	title,
	value,
	trendingUp,
	trendingValue,
	icon,
}: SummaryCardsProps) {
	return (
		<div
			className={clsx(
				"bg-white shadow-md rounded-lg p-4 flex items-center justify-between border-l-3",
				trendingUp ? "border-green-500" : "border-gray-400",
			)}
		>
			<div>
				<h2 className="text-sm text-neutral-500">{title}</h2>
				<p className="text-2xl font-semibold">{value}</p>
				<div
					className={clsx("flex items-center text-sm", {
						"text-green-500": trendingUp,
						"text-gray-400": !trendingUp,
					})}
				>
					{trendingUp ? (
						<ArrowUp className="h-4 w-4 mr-1" />
					) : (
						<ArrowDown className="h-4 w-4 mr-1" />
					)}
					{trendingValue}%
				</div>
			</div>
			{icon}
		</div>
	);
}
