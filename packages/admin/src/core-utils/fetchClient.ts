type FetchOptions = RequestInit & {
	baseUrl?: string;
	timeout?: number;
	headers?: Record<string, string>;
	isFormData?: boolean;
};

type ErrorDataResponse = {
	message?: string;
};

const DEFAULT_OPTIONS: FetchOptions = {
	baseUrl: import.meta.env.VITE_API_URL,
	timeout: 10000,
	headers: {
		"Content-Type": "application/json",
	},
	isFormData: false,
};

class FetchClient {
	private baseUrl: string;
	private defaultOptions: FetchOptions;

	constructor(options: FetchOptions = {}) {
		this.defaultOptions = { ...DEFAULT_OPTIONS, ...options };
		this.baseUrl = this.defaultOptions.baseUrl || "";
	}

	private createUrl(endpoint: string): string {
		return `${this.baseUrl}${endpoint}`;
	}

	private async fetchWithTimeout(
		url: string,
		options: RequestInit,
	): Promise<Response> {
		const { timeout } = this.defaultOptions;

		console.log("Making request to:", url);
		console.log("Request options:", JSON.stringify(options, null, 2));

		if (!timeout) {
			return fetch(url, options);
		}

		const controller = new AbortController();
		const timeoutId = setTimeout(() => controller.abort(), timeout);

		try {
			const response = await fetch(url, {
				...options,
				signal: controller.signal,
			});
			console.log("Response status:", response.status);
			return response;
		} catch (error) {
			clearTimeout(timeoutId);
			console.error("Fetch error:", error);
			throw error;
		} finally {
			clearTimeout(timeoutId);
		}
	}

	private async handleResponse<T>(response: Response): Promise<T> {
		console.log(
			"Response headers:",
			JSON.stringify(
				Object.fromEntries([...response.headers.entries()]),
				null,
				2,
			),
		);

		if (!response.ok) {
			let errorData: ErrorDataResponse;
			const contentType = response.headers.get("content-type");
			console.log("Error response content type:", contentType);

			if (contentType?.includes("application/json")) {
				try {
					errorData = await response.json();
					console.log("Error response JSON:", errorData);
				} catch (e) {
					console.error("Failed to parse error response as JSON:", e);
					errorData = { message: "Unknown error" };
				}
			} else {
				try {
					const text = await response.text();
					console.log("Error response text:", text);
					errorData = { message: text };
				} catch (e) {
					console.error("Failed to get error response text:", e);
					errorData = { message: "Unknown error" };
				}
			}

			throw new Error(
				errorData.message || `Error ${response.status}: ${response.statusText}`,
			);
		}

		if (
			response.status === 204 ||
			response.headers.get("content-length") === "0"
		) {
			console.log("Empty response, returning empty object");
			return {} as T;
		}

		// check content type to determine how to parse Response
		const contentType = response.headers.get("content-type");
		console.log("Response content type:", contentType);

		if (contentType?.includes("application/json")) {
			try {
				const jsonData = await response.json();
				console.log("Response JSON:", jsonData);
				return jsonData;
			} catch (e) {
				console.error("Failed to parse response as JSON:", e);
				throw new Error("Failed to parse response as JSON");
			}
		} else {
			console.log("Returning response as is");
			return response as unknown as T;
		}
	}

	// Prepare request options based on data type
	private prepareRequestOptions(
		options: RequestInit,
		// biome-ignore lint/suspicious/noExplicitAny: <explanation>
		data?: any,
		isFormData?: boolean,
	): RequestInit {
		const requestOptions = { ...options };

		if (!data) {
			return requestOptions;
		}

		// handle formData
		if (isFormData || data instanceof FormData) {
			// don't set Content-Type for FormData since browser will define it in boundary
			if (requestOptions.headers) {
				const headers = { ...requestOptions.headers } as Record<string, string>;
				// biome-ignore lint/performance/noDelete: <explanation>
				delete headers["Content-Type"];
				requestOptions.headers = headers;
			}
			requestOptions.body =
				data instanceof FormData ? data : this.convertToFormData(data);
			return requestOptions;
		}

		requestOptions.body =
			typeof data === "string" ? data : JSON.stringify(data);
		return requestOptions;
	}

	// convert object to FormData
	// biome-ignore lint/suspicious/noExplicitAny: <explanation>
	private convertToFormData(data: Record<string, any>): FormData {
		const formData = new FormData();

		// biome-ignore lint/complexity/noForEach: <explanation>
		Object.entries(data).forEach(([key, val]) => {
			if (Array.isArray(val)) {
				// biome-ignore lint/complexity/noForEach: <explanation>
				val.forEach((item) => {
					formData.append(key, item);
				});
			} else if (val instanceof Blob || val instanceof File) {
				formData.append(key, val);
			} else if (typeof val === "object") {
				formData.append(key, JSON.stringify(val));
			} else {
				formData.append(key, String(val));
			}
		});
		return formData;
	}

	async get<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
		const url = this.createUrl(endpoint);
		const response = await this.fetchWithTimeout(url, {
			...this.defaultOptions,
			...options,
			method: "GET",
		});
		return this.handleResponse<T>(response);
	}

	async post<T>(
		endpoint: string,
		options: RequestInit & { isFormData?: boolean } = {},
		// biome-ignore lint/suspicious/noExplicitAny: <explanation>
		data?: any,
	): Promise<T> {
		console.log("POST request to endpoint:", endpoint);
		console.log("POST request data:", data);

		const url = this.createUrl(endpoint);
		console.log("Full URL:", url);

		const isFormData = options.isFormData ?? this.defaultOptions.isFormData;
		console.log("Is FormData:", isFormData);

		const requestOptions = this.prepareRequestOptions(
			{
				...this.defaultOptions,
				...options,
				method: "POST",
			},
			data,
			isFormData,
		);

		console.log(
			"Prepared request options:",
			JSON.stringify(
				requestOptions,
				(key, value) => {
					// Don't stringify the body if it's a string (already stringified)
					if (key === "body" && typeof value === "string") {
						return value.length > 100 ? `${value.substring(0, 100)}...` : value;
					}
					return value;
				},
				2,
			),
		);

		try {
			const response = await this.fetchWithTimeout(url, requestOptions);
			return this.handleResponse(response);
		} catch (error) {
			console.error("POST request failed:", error);
			throw error;
		}
	}

	async put<T>(
		endpoint: string,
		options: RequestInit & { isFormData?: boolean },
		// biome-ignore lint/suspicious/noExplicitAny: <explanation>
		data?: any,
	): Promise<T> {
		const url = this.createUrl(endpoint);
		const isFormData = options.isFormData ?? this.defaultOptions.isFormData;
		const requestOptions = this.prepareRequestOptions(
			{
				...this.defaultOptions,
				...options,
				method: "PUT",
			},
			data,
			isFormData,
		);

		const response = await this.fetchWithTimeout(url, requestOptions);
		return this.handleResponse(response);
	}

	async patch<T>(
		endpoint: string,
		options: RequestInit & { isFormData?: boolean },
		// biome-ignore lint/suspicious/noExplicitAny: <explanation>
		data?: any,
	): Promise<T> {
		const url = this.createUrl(endpoint);
		const isFormData = options.isFormData ?? this.defaultOptions.isFormData;
		const requestOptions = this.prepareRequestOptions(
			{
				...this.defaultOptions,
				...options,
				method: "PATCH",
			},
			data,
			isFormData,
		);

		const response = await this.fetchWithTimeout(url, requestOptions);
		return this.handleResponse<T>(response);
	}

	async delete<T>(
		endpoint: string,
		options: RequestInit & { isFormData?: boolean } = {},
		// biome-ignore lint/suspicious/noExplicitAny: <explanation>
		data?: any,
	): Promise<T> {
		const url = this.createUrl(endpoint);
		const isFormData = options.isFormData ?? this.defaultOptions.isFormData;
		const requestOptions = this.prepareRequestOptions(
			{
				...this.defaultOptions,
				...options,
				method: "DELETE",
			},
			data,
			isFormData,
		);

		const response = await this.fetchWithTimeout(url, requestOptions);
		return this.handleResponse<T>(response);
	}
}

export default FetchClient;
