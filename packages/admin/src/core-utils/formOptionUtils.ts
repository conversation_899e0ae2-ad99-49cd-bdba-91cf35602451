import type { PartsType, SupplierType } from "@/data/parts-schema-ui";
import { formOptions } from "@tanstack/react-form";

export const partsFormOptions = formOptions({
	defaultValues: {
		name: "",
		status: "IN_STOCK",
		category: "ACCESSORIES",
		minQuantity: 0,
		costPerUnit: 0,
		unit: "",
		supplier: "",
		createdAt: new Date(),
		sku: "",
		quantity: 0,
	} as PartsType,
});

export const supplierFormOptions = formOptions({
	defaultValues: {
		name: "",
		email: "",
		phone: "",
		address: "",
		website: "",
		notes: "",
		contactName: "",
	} as SupplierType,
});
