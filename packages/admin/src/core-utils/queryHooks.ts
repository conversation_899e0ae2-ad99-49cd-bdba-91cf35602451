import {
	type QueryKey,
	type UseMutationOptions,
	type UseQueryOptions,
	useMutation,
	useQuery,
	useQueryClient,
} from "@tanstack/react-query";
import FetchClient from "./fetchClient";

// singleton instance of FetchClient to use used across app
export const createApiClient = (config = {}) => new FetchClient(config);

// default API client
let apiClient = createApiClient();

// configure api client (call during app initialization)
// biome-ignore lint/suspicious/noExplicitAny: <explanation>
export const configureApiClient = (config: any) => {
	apiClient = createApiClient(config);
	return apiClient;
};

// queryKey factory to ensure consistent key structure
export const queryKeys = {
	all: ["api"] as const,
	lists: () => [...queryKeys.all, "list"] as const,
	list: (entity: string) => [...queryKeys.lists(), entity] as const,
	details: () => [...queryKeys.all, "detail"] as const,
	detail: (entity: string, id: string | number) =>
		[...queryKeys.details(), entity, id] as const,
};

// mutationKey factory to ensure consistent mutation key structure
export const mutationKeys = {
	all: ["mutation"] as const,
	creates: () => [...mutationKeys.all, "create"] as const,
	create: (entity: string) => [...mutationKeys.creates(), entity] as const,
	updates: () => [...mutationKeys.all, "update"] as const,
	update: (entity: string, id?: string | number) =>
		id !== undefined
			? ([...mutationKeys.updates(), entity, id] as const)
			: ([...mutationKeys.updates(), entity] as const),
	deletes: () => [...mutationKeys.all, "delete"] as const,
	delete: (entity: string, id?: string | number) =>
		id !== undefined
			? ([...mutationKeys.deletes(), entity, id] as const)
			: ([...mutationKeys.deletes(), entity] as const),
	batches: () => [...mutationKeys.all, "batch"] as const,
	batch: (entity: string, operation: "create" | "update" | "delete") =>
		[...mutationKeys.batches(), entity, operation] as const,
};

// custom hook for GET requests
export function useApiQuery<TData = unknown, TError = Error>(
	queryKey: QueryKey,
	endpoint: string,
	options?: Omit<
		UseQueryOptions<TData, TError, TData, QueryKey>,
		"queryKey" | "queryFn"
	>,
) {
	return useQuery<TData, TError>({
		queryKey,
		queryFn: () => apiClient.get<TData>(endpoint),
		...options,
	});
}

interface FormDataOptions {
	isFormData?: boolean;
}

// helper for mutation hooks with automatic cache invalidation
// biome-ignore lint/suspicious/noExplicitAny: <explanation>
function createMutationHook<TData = unknown, TError = Error, TVariables = any>(
	method: "post" | "put" | "patch" | "delete",
) {
	return (
		endpoint: string,
		options?: Omit<
			UseMutationOptions<TData, TError, TVariables>,
			"mutationFn"
		> & {
			invalidateQueries?: QueryKey | QueryKey[];
			exactInvalidation?: boolean;
			formDataOptions?: FormDataOptions;
		},
	) => {
		const queryClient = useQueryClient();
		const {
			invalidateQueries,
			exactInvalidation,
			formDataOptions,
			...mutationOptions
		} = options || {};

		return useMutation<TData, TError, TVariables>({
			// biome-ignore lint/suspicious/noExplicitAny: <explanation>
			mutationFn: (variables: any) => {
				if (method === "delete" && !variables) {
					return apiClient.delete<TData>(endpoint);
				}
				return apiClient[method]<TData>(
					endpoint,
					{
						isFormData: formDataOptions?.isFormData,
					},
					variables,
				);
			},
			onSuccess: async (data, variables, context) => {
				// run user's onSuccess if provided
				if (mutationOptions.onSuccess) {
					await mutationOptions.onSuccess(data, variables, context);
				}

				// auto-invalidate queries if specified
				if (invalidateQueries) {
					const queryKeys = Array.isArray(invalidateQueries[0])
						? (invalidateQueries as QueryKey[])
						: [invalidateQueries as QueryKey];

					for (const key of queryKeys) {
						queryClient.invalidateQueries({
							queryKey: key,
							exact: Boolean(exactInvalidation),
							refetchType: "active",
						});
					}
				}
			},
			...mutationOptions,
		});
	};
}

// custom hooks for various mutation types with auto cache invalidation
export const useApiMutation = createMutationHook("post");
export const useApiPutMutation = createMutationHook("put");
export const useApiPatchMutation = createMutationHook("patch");
export const useApiDeleteMutation = createMutationHook("delete");

// hook for handling FormData submissions
export function useFormDataMutation<
	TData = unknown,
	TError = Error,
	// biome-ignore lint/suspicious/noExplicitAny: <explanation>
	TVariables = any,
>(
	endpoint: string,
	options?: Omit<
		UseMutationOptions<TData, TError, TVariables>,
		"mutationFn"
	> & {
		invalidateQueries?: QueryKey | QueryKey[];
		exactInvalidation?: boolean;
		method?: "post" | "put" | "patch";
	},
) {
	const { method = "post", ...rest } = options || {};

	const mutationHook = createMutationHook<TData, TError, TVariables>(method);
	return mutationHook(endpoint, {
		...rest,
		formDataOptions: { isFormData: true },
	});
}

// hook to create more selective invalidations
export function useInvalidateQueries() {
	const queryClient = useQueryClient();

	return {
		invalidate: (queryKey: QueryKey, exact = false) => {
			return queryClient.invalidateQueries({
				queryKey,
				exact,
				refetchType: "active",
			});
		},
		invalidateAll: () => {
			return queryClient.invalidateQueries({ queryKey: queryKeys.all });
		},
		invalidateEntity: (entity: string) => {
			return Promise.all([
				queryClient.invalidateQueries({
					queryKey: [...queryKeys.list(entity)],
				}),
				queryClient.invalidateQueries({
					queryKey: [...queryKeys.details(), entity],
				}),
			]);
		},
		invalidateList: (entity: string) => {
			return queryClient.invalidateQueries({
				queryKey: [...queryKeys.list(entity)],
			});
		},
		invalidateDetail: (entity: string, id: string | number) => {
			return queryClient.invalidateQueries({
				queryKey: [...queryKeys.detail(entity, id)],
				exact: true,
			});
		},
	};
}

// prefetch hook
export function usePrefetchQuery() {
	const queryClient = useQueryClient();

	return {
		prefetchList: async (entity: string, endpoint: string) => {
			await queryClient.prefetchQuery({
				queryKey: queryKeys.list(entity),
				queryFn: () => apiClient.get(endpoint),
			});
		},
		prefetchDetail: async (
			entity: string,
			id: string | number,
			endpoint: string,
		) => {
			await queryClient.prefetchQuery({
				queryKey: queryKeys.detail(entity, id),
				queryFn: () => apiClient.get(endpoint),
			});
		},
	};
}
