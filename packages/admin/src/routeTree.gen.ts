/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

// Import Routes

import { Route as rootRoute } from './routes/__root'
import { Route as SignupImport } from './routes/signup'
import { Route as HomeRouteImport } from './routes/home/<USER>'
import { Route as IndexImport } from './routes/index'
import { Route as SignUpSplatImport } from './routes/sign-up.$'
import { Route as SignInSplatImport } from './routes/sign-in.$'
import { Route as HomePurchaseOrdersImport } from './routes/home/<USER>'
import { Route as HomeOverviewImport } from './routes/home/<USER>'
import { Route as HomeGettingStartedImport } from './routes/home/<USER>'
import { Route as HomeInventoryVehiclesImport } from './routes/home/<USER>/vehicles'
import { Route as HomeInventoryPartsImport } from './routes/home/<USER>/parts'
import { Route as HomeInventoryAddImport } from './routes/home/<USER>/add'

// Create/Update Routes

const SignupRoute = SignupImport.update({
  id: '/signup',
  path: '/signup',
  getParentRoute: () => rootRoute,
} as any)

const HomeRouteRoute = HomeRouteImport.update({
  id: '/home',
  path: '/home',
  getParentRoute: () => rootRoute,
} as any)

const IndexRoute = IndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRoute,
} as any)

const SignUpSplatRoute = SignUpSplatImport.update({
  id: '/sign-up/$',
  path: '/sign-up/$',
  getParentRoute: () => rootRoute,
} as any)

const SignInSplatRoute = SignInSplatImport.update({
  id: '/sign-in/$',
  path: '/sign-in/$',
  getParentRoute: () => rootRoute,
} as any)

const HomePurchaseOrdersRoute = HomePurchaseOrdersImport.update({
  id: '/purchase-orders',
  path: '/purchase-orders',
  getParentRoute: () => HomeRouteRoute,
} as any)

const HomeOverviewRoute = HomeOverviewImport.update({
  id: '/overview',
  path: '/overview',
  getParentRoute: () => HomeRouteRoute,
} as any)

const HomeGettingStartedRoute = HomeGettingStartedImport.update({
  id: '/getting-started',
  path: '/getting-started',
  getParentRoute: () => HomeRouteRoute,
} as any)

const HomeInventoryVehiclesRoute = HomeInventoryVehiclesImport.update({
  id: '/inventory/vehicles',
  path: '/inventory/vehicles',
  getParentRoute: () => HomeRouteRoute,
} as any)

const HomeInventoryPartsRoute = HomeInventoryPartsImport.update({
  id: '/inventory/parts',
  path: '/inventory/parts',
  getParentRoute: () => HomeRouteRoute,
} as any)

const HomeInventoryAddRoute = HomeInventoryAddImport.update({
  id: '/inventory/add',
  path: '/inventory/add',
  getParentRoute: () => HomeRouteRoute,
} as any)

// Populate the FileRoutesByPath interface

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexImport
      parentRoute: typeof rootRoute
    }
    '/home': {
      id: '/home'
      path: '/home'
      fullPath: '/home'
      preLoaderRoute: typeof HomeRouteImport
      parentRoute: typeof rootRoute
    }
    '/signup': {
      id: '/signup'
      path: '/signup'
      fullPath: '/signup'
      preLoaderRoute: typeof SignupImport
      parentRoute: typeof rootRoute
    }
    '/home/<USER>': {
      id: '/home/<USER>'
      path: '/getting-started'
      fullPath: '/home/<USER>'
      preLoaderRoute: typeof HomeGettingStartedImport
      parentRoute: typeof HomeRouteImport
    }
    '/home/<USER>': {
      id: '/home/<USER>'
      path: '/overview'
      fullPath: '/home/<USER>'
      preLoaderRoute: typeof HomeOverviewImport
      parentRoute: typeof HomeRouteImport
    }
    '/home/<USER>': {
      id: '/home/<USER>'
      path: '/purchase-orders'
      fullPath: '/home/<USER>'
      preLoaderRoute: typeof HomePurchaseOrdersImport
      parentRoute: typeof HomeRouteImport
    }
    '/sign-in/$': {
      id: '/sign-in/$'
      path: '/sign-in/$'
      fullPath: '/sign-in/$'
      preLoaderRoute: typeof SignInSplatImport
      parentRoute: typeof rootRoute
    }
    '/sign-up/$': {
      id: '/sign-up/$'
      path: '/sign-up/$'
      fullPath: '/sign-up/$'
      preLoaderRoute: typeof SignUpSplatImport
      parentRoute: typeof rootRoute
    }
    '/home/<USER>/add': {
      id: '/home/<USER>/add'
      path: '/inventory/add'
      fullPath: '/home/<USER>/add'
      preLoaderRoute: typeof HomeInventoryAddImport
      parentRoute: typeof HomeRouteImport
    }
    '/home/<USER>/parts': {
      id: '/home/<USER>/parts'
      path: '/inventory/parts'
      fullPath: '/home/<USER>/parts'
      preLoaderRoute: typeof HomeInventoryPartsImport
      parentRoute: typeof HomeRouteImport
    }
    '/home/<USER>/vehicles': {
      id: '/home/<USER>/vehicles'
      path: '/inventory/vehicles'
      fullPath: '/home/<USER>/vehicles'
      preLoaderRoute: typeof HomeInventoryVehiclesImport
      parentRoute: typeof HomeRouteImport
    }
  }
}

// Create and export the route tree

interface HomeRouteRouteChildren {
  HomeGettingStartedRoute: typeof HomeGettingStartedRoute
  HomeOverviewRoute: typeof HomeOverviewRoute
  HomePurchaseOrdersRoute: typeof HomePurchaseOrdersRoute
  HomeInventoryAddRoute: typeof HomeInventoryAddRoute
  HomeInventoryPartsRoute: typeof HomeInventoryPartsRoute
  HomeInventoryVehiclesRoute: typeof HomeInventoryVehiclesRoute
}

const HomeRouteRouteChildren: HomeRouteRouteChildren = {
  HomeGettingStartedRoute: HomeGettingStartedRoute,
  HomeOverviewRoute: HomeOverviewRoute,
  HomePurchaseOrdersRoute: HomePurchaseOrdersRoute,
  HomeInventoryAddRoute: HomeInventoryAddRoute,
  HomeInventoryPartsRoute: HomeInventoryPartsRoute,
  HomeInventoryVehiclesRoute: HomeInventoryVehiclesRoute,
}

const HomeRouteRouteWithChildren = HomeRouteRoute._addFileChildren(
  HomeRouteRouteChildren,
)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/home': typeof HomeRouteRouteWithChildren
  '/signup': typeof SignupRoute
  '/home/<USER>': typeof HomeGettingStartedRoute
  '/home/<USER>': typeof HomeOverviewRoute
  '/home/<USER>': typeof HomePurchaseOrdersRoute
  '/sign-in/$': typeof SignInSplatRoute
  '/sign-up/$': typeof SignUpSplatRoute
  '/home/<USER>/add': typeof HomeInventoryAddRoute
  '/home/<USER>/parts': typeof HomeInventoryPartsRoute
  '/home/<USER>/vehicles': typeof HomeInventoryVehiclesRoute
}

export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/home': typeof HomeRouteRouteWithChildren
  '/signup': typeof SignupRoute
  '/home/<USER>': typeof HomeGettingStartedRoute
  '/home/<USER>': typeof HomeOverviewRoute
  '/home/<USER>': typeof HomePurchaseOrdersRoute
  '/sign-in/$': typeof SignInSplatRoute
  '/sign-up/$': typeof SignUpSplatRoute
  '/home/<USER>/add': typeof HomeInventoryAddRoute
  '/home/<USER>/parts': typeof HomeInventoryPartsRoute
  '/home/<USER>/vehicles': typeof HomeInventoryVehiclesRoute
}

export interface FileRoutesById {
  __root__: typeof rootRoute
  '/': typeof IndexRoute
  '/home': typeof HomeRouteRouteWithChildren
  '/signup': typeof SignupRoute
  '/home/<USER>': typeof HomeGettingStartedRoute
  '/home/<USER>': typeof HomeOverviewRoute
  '/home/<USER>': typeof HomePurchaseOrdersRoute
  '/sign-in/$': typeof SignInSplatRoute
  '/sign-up/$': typeof SignUpSplatRoute
  '/home/<USER>/add': typeof HomeInventoryAddRoute
  '/home/<USER>/parts': typeof HomeInventoryPartsRoute
  '/home/<USER>/vehicles': typeof HomeInventoryVehiclesRoute
}

export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/home'
    | '/signup'
    | '/home/<USER>'
    | '/home/<USER>'
    | '/home/<USER>'
    | '/sign-in/$'
    | '/sign-up/$'
    | '/home/<USER>/add'
    | '/home/<USER>/parts'
    | '/home/<USER>/vehicles'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | '/home'
    | '/signup'
    | '/home/<USER>'
    | '/home/<USER>'
    | '/home/<USER>'
    | '/sign-in/$'
    | '/sign-up/$'
    | '/home/<USER>/add'
    | '/home/<USER>/parts'
    | '/home/<USER>/vehicles'
  id:
    | '__root__'
    | '/'
    | '/home'
    | '/signup'
    | '/home/<USER>'
    | '/home/<USER>'
    | '/home/<USER>'
    | '/sign-in/$'
    | '/sign-up/$'
    | '/home/<USER>/add'
    | '/home/<USER>/parts'
    | '/home/<USER>/vehicles'
  fileRoutesById: FileRoutesById
}

export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  HomeRouteRoute: typeof HomeRouteRouteWithChildren
  SignupRoute: typeof SignupRoute
  SignInSplatRoute: typeof SignInSplatRoute
  SignUpSplatRoute: typeof SignUpSplatRoute
}

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  HomeRouteRoute: HomeRouteRouteWithChildren,
  SignupRoute: SignupRoute,
  SignInSplatRoute: SignInSplatRoute,
  SignUpSplatRoute: SignUpSplatRoute,
}

export const routeTree = rootRoute
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()

/* ROUTE_MANIFEST_START
{
  "routes": {
    "__root__": {
      "filePath": "__root.tsx",
      "children": [
        "/",
        "/home",
        "/signup",
        "/sign-in/$",
        "/sign-up/$"
      ]
    },
    "/": {
      "filePath": "index.tsx"
    },
    "/home": {
      "filePath": "home/route.tsx",
      "children": [
        "/home/<USER>",
        "/home/<USER>",
        "/home/<USER>",
        "/home/<USER>/add",
        "/home/<USER>/parts",
        "/home/<USER>/vehicles"
      ]
    },
    "/signup": {
      "filePath": "signup.tsx"
    },
    "/home/<USER>": {
      "filePath": "home/getting-started.tsx",
      "parent": "/home"
    },
    "/home/<USER>": {
      "filePath": "home/overview.tsx",
      "parent": "/home"
    },
    "/home/<USER>": {
      "filePath": "home/purchase-orders.tsx",
      "parent": "/home"
    },
    "/sign-in/$": {
      "filePath": "sign-in.$.tsx"
    },
    "/sign-up/$": {
      "filePath": "sign-up.$.tsx"
    },
    "/home/<USER>/add": {
      "filePath": "home/inventory/add.tsx",
      "parent": "/home"
    },
    "/home/<USER>/parts": {
      "filePath": "home/inventory/parts.tsx",
      "parent": "/home"
    },
    "/home/<USER>/vehicles": {
      "filePath": "home/inventory/vehicles.tsx",
      "parent": "/home"
    }
  }
}
ROUTE_MANIFEST_END */
