import { But<PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import {
	SignInButton,
	SignOutButton,
	SignedIn,
	SignedOut,
} from "@clerk/clerk-react";
import { Link, createFileRoute } from "@tanstack/react-router";
import { Car, DollarSign, Target, TrendingUp, Users } from "lucide-react";

export const Route = createFileRoute("/")({
	component: App,
});

function App() {
	return (
		<div className="flex flex-col flex-1 w-full min-h-screen bg-accent">
			<div className="w-full md:w-[1024px] mx-auto px-4 md:px-0">
				<SignedIn>
					{/* Sticky Header with logo and buttons */}
					<div className="sticky top-0 z-10 py-4 bg-accent/95 backdrop-blur-sm border-b w-full">
						<div className="flex items-center justify-between">
							{/* Logo Section */}
							<div className="flex items-center">
								<Link to="/" className="flex items-center gap-2">
									{/* You can replace this with your actual logo component or image */}
									<div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
										<span className="text-white font-bold">R</span>
									</div>
									<span className="font-semibold text-lg hidden sm:inline-block">
										RONOR MOTORS
									</span>
								</Link>
							</div>

							{/* Buttons Section */}
							<div className="flex gap-2 sm:gap-4">
								<Button asChild className="bg-primary hover:bg-primary/90">
									<Link to="/home/<USER>">Go to Dashboard</Link>
								</Button>
								<SignOutButton />
							</div>
						</div>
					</div>

					{/* Bento Grid Layout */}
					<div className="mt-8 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 auto-rows-[180px]">
						{/* Featured Card - 2x2 */}
						<div className="bg-white/20 backdrop-blur-sm rounded-lg p-4 sm:p-6 shadow-lg md:col-span-2 lg:col-span-2 md:row-span-2 transition-transform hover:scale-[1.02]">
							<div className="flex flex-col h-full">
								<div className="flex items-center justify-between mb-4">
									<div>
										<h3 className="text-xl font-semibold mb-1">
											Performance Overview
										</h3>
										<p className="text-sm opacity-75">
											Last 6 months of sales pipeline
										</p>
									</div>
									<div className="flex gap-3">
										<div className="flex items-center gap-1.5 text-xs">
											<div className="w-2 h-2 rounded-full bg-primary/80" />
											<span>Closed Deals</span>
										</div>
										<div className="flex items-center gap-1.5 text-xs">
											<div className="w-2 h-2 rounded-full bg-rose-500/50" />
											<span>Open Inquiries</span>
										</div>
									</div>
								</div>

								{/* Key Metrics Grid */}
								<div className="grid grid-cols-3 gap-4 mb-1">
									<div className="bg-white/10 rounded-lg p-3">
										<div className="flex items-center gap-2 mb-2">
											<DollarSign className="w-4 h-4 text-primary" />
											<span className="text-sm font-medium">Revenue</span>
										</div>
										<div className="flex items-baseline gap-2">
											<span className="text-2xl font-bold">$842K</span>
											<div className="flex items-center text-emerald-500 text-xs">
												<TrendingUp className="w-3 h-3" />
												<span>18.2%</span>
											</div>
										</div>
									</div>

									<div className="bg-white/10 rounded-lg p-3">
										<div className="flex items-center gap-2 mb-2">
											<Target className="w-4 h-4 text-primary" />
											<span className="text-sm font-medium">Conversion</span>
										</div>
										<div className="flex items-baseline gap-2">
											<span className="text-2xl font-bold">26.8%</span>
											<div className="flex items-center text-emerald-500 text-xs">
												<TrendingUp className="w-3 h-3" />
												<span>2.4%</span>
											</div>
										</div>
									</div>

									<div className="bg-white/10 rounded-lg p-3">
										<div className="flex items-center gap-2 mb-2">
											<Users className="w-4 h-4 text-primary" />
											<span className="text-sm font-medium">
												Avg. Deal Time
											</span>
										</div>
										<div className="flex items-baseline gap-2">
											<span className="text-2xl font-bold">12.4</span>
											<span className="text-sm opacity-75">days</span>
										</div>
									</div>
								</div>

								{/* New Metrics Section */}
								<div className="flex-1 grid grid-cols-3 gap-4 mt-4">
									<div className="bg-white/10 rounded-lg p-4">
										<h4 className="text-sm font-medium mb-3">
											Recent Sales Revenue
										</h4>
										<div className="space-y-2">
											<div className="flex justify-between items-center">
												<span className="text-sm opacity-75">This Week</span>
												<span className="font-medium">$156,800</span>
											</div>
											<div className="flex justify-between items-center">
												<span className="text-sm opacity-75">Last Week</span>
												<span className="font-medium">$142,500</span>
											</div>
										</div>
									</div>

									<div className="bg-white/10 rounded-lg p-4">
										<h4 className="text-sm font-medium mb-3">
											Outstanding Payments
										</h4>
										<div className="space-y-2">
											<div className="flex justify-between items-center">
												<span className="text-sm opacity-75">Pending</span>
												<span className="font-medium">$234,500</span>
											</div>
											<div className="flex justify-between items-center">
												<span className="text-sm opacity-75">
													In Processing
												</span>
												<span className="font-medium">$89,200</span>
											</div>
										</div>
									</div>

									<div className="bg-white/10 rounded-lg p-4">
										<h4 className="text-sm font-medium mb-3">
											Commission Stats
										</h4>
										<div className="space-y-2">
											<div className="flex justify-between items-center">
												<span className="text-sm opacity-75">MTD Earned</span>
												<span className="font-medium">$12,450</span>
											</div>
											<div className="flex justify-between items-center">
												<span className="text-sm opacity-75">
													Target Progress
												</span>
												<span className="font-medium">78%</span>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>

						{/* Quick Actions Card */}
						<div className="bg-white/20 backdrop-blur-sm rounded-lg p-4 sm:p-6 shadow-lg transition-transform hover:scale-[1.02]">
							<div className="flex flex-col h-full">
								<h3 className="text-lg font-semibold mb-3">Quick Actions</h3>
								<div className="grid grid-cols-2 gap-2">
									<Button
										variant="outline"
										className="bg-white/10 hover:bg-white/20 h-auto py-2 flex flex-col gap-1"
										asChild
									>
										<Link to="/">
											<Car className="w-4 h-4" />
											<span className="text-xs">New Vehicle</span>
										</Link>
									</Button>
									<Button
										variant="outline"
										className="bg-white/10 hover:bg-white/20 h-auto py-2 flex flex-col gap-1"
										asChild
									>
										<Link to="/">
											<DollarSign className="w-4 h-4" />
											<span className="text-xs">New Sale</span>
										</Link>
									</Button>
									<Button
										variant="outline"
										className="bg-white/10 hover:bg-white/20 h-auto py-2 flex flex-col gap-1"
										asChild
									>
										<Link to="/">
											<TrendingUp className="w-4 h-4" />
											<span className="text-xs">Reports</span>
										</Link>
									</Button>
									<Button
										variant="outline"
										className="bg-white/10 hover:bg-white/20 h-auto py-2 flex flex-col gap-1"
										asChild
									>
										<Link to="/">
											<Users className="w-4 h-4" />
											<span className="text-xs">Leads</span>
										</Link>
									</Button>
								</div>
							</div>
						</div>

						{/* Leads Summary Card */}
						<div className="bg-white/20 backdrop-blur-sm rounded-lg p-4 sm:p-6 shadow-lg transition-transform hover:scale-[1.02]">
							<div className="flex flex-col h-full">
								<div className="flex items-center justify-between mb-2">
									<h3 className="text-lg font-semibold">New Leads</h3>
									<div className="p-2 bg-white/10 rounded-full">
										<Users className="w-4 h-4" />
									</div>
								</div>

								<div className="flex items-baseline gap-2">
									<span className="text-3xl font-bold">127</span>
									<span className="text-sm opacity-75">this month</span>
								</div>

								<div className="flex items-center gap-1 mt-2">
									<div className="flex items-center gap-1 text-emerald-500">
										<TrendingUp className="w-4 h-4" />
										<span className="text-sm font-medium">12.5%</span>
									</div>
									<span className="text-xs opacity-75">vs last month</span>
								</div>
							</div>
						</div>

						{/* Messages Card - Now spans 1x2 */}
						<div className="bg-white/20 backdrop-blur-sm rounded-lg p-4 sm:p-6 shadow-lg md:row-span-2 transition-transform hover:scale-[1.02] flex flex-col">
							<div className="flex items-center justify-between mb-4">
								<h3 className="text-lg font-semibold">Unread Messages</h3>
								<span className="bg-primary/90 text-white text-xs px-2 py-1 rounded-full">
									4 new
								</span>
							</div>
							<div className="space-y-3 overflow-y-auto flex-1 min-h-0">
								<div className="flex gap-3 items-start p-2 rounded-md hover:bg-white/10">
									<div className="w-2 h-2 mt-2 rounded-full bg-primary" />
									<div>
										<p className="text-sm font-medium">Service Reminder</p>
										<p className="text-xs opacity-75">
											Vehicle #123 is due for maintenance
										</p>
										<span className="text-xs opacity-60">2 hours ago</span>
									</div>
								</div>

								<div className="flex gap-3 items-start p-2 rounded-md hover:bg-white/10">
									<div className="w-2 h-2 mt-2 rounded-full bg-primary" />
									<div>
										<p className="text-sm font-medium">New Booking Request</p>
										<p className="text-xs opacity-75">
											Customer requested vehicle inspection
										</p>
										<span className="text-xs opacity-60">5 hours ago</span>
									</div>
								</div>

								<div className="flex gap-3 items-start p-2 rounded-md hover:bg-white/10">
									<div className="w-2 h-2 mt-2 rounded-full bg-primary" />
									<div>
										<p className="text-sm font-medium">Inventory Alert</p>
										<p className="text-xs opacity-75">
											Low stock on spare parts
										</p>
										<span className="text-xs opacity-60">Yesterday</span>
									</div>
								</div>

								<div className="flex gap-3 items-start p-2 rounded-md hover:bg-white/10">
									<div className="w-2 h-2 mt-2 rounded-full bg-primary" />
									<div>
										<p className="text-sm font-medium">Payment Received</p>
										<p className="text-xs opacity-75">
											Invoice #987 has been paid
										</p>
										<span className="text-xs opacity-60">Yesterday</span>
									</div>
								</div>
							</div>
							<Button
								variant="ghost"
								className="w-full mt-4 text-sm hover:bg-white/10"
								asChild
							>
								<Link to="/">View All Messages</Link>
							</Button>
						</div>

						{/* Vehicle Performance Card - Now spans 2x2 */}
						<div className="bg-white/20 backdrop-blur-sm rounded-lg p-4 sm:p-6 shadow-lg md:col-span-2 md:row-span-2 transition-transform hover:scale-[1.02]">
							<div className="flex flex-col h-full">
								<div className="flex items-center justify-between mb-3">
									<div>
										<h3 className="text-lg font-semibold leading-none mb-1">
											Vehicle Performance
										</h3>
										<p className="text-xs opacity-75">
											Top sellers and inventory insights
										</p>
									</div>
									<div className="p-2 bg-white/10 rounded-full">
										<Car className="w-4 h-4" />
									</div>
								</div>

								<div className="grid grid-cols-2 gap-4 flex-1">
									<div className="space-y-3">
										<h4 className="text-sm font-medium">
											Top Performing Models
										</h4>
										<div className="space-y-2">
											<div className="flex justify-between items-center">
												<div className="flex items-center gap-2">
													<div className="w-1.5 h-1.5 rounded-full bg-emerald-500" />
													<span className="text-sm">Tesla Model Y</span>
												</div>
												<div className="flex items-center gap-1 text-emerald-500">
													<TrendingUp className="w-3 h-3" />
													<span className="text-xs">8 sold</span>
												</div>
											</div>
											<div className="flex justify-between items-center">
												<div className="flex items-center gap-2">
													<div className="w-1.5 h-1.5 rounded-full bg-emerald-500" />
													<span className="text-sm">BMW X5</span>
												</div>
												<div className="flex items-center gap-1 text-emerald-500">
													<TrendingUp className="w-3 h-3" />
													<span className="text-xs">6 sold</span>
												</div>
											</div>
										</div>
									</div>

									<div className="space-y-3">
										<h4 className="text-sm font-medium">Needs Attention</h4>
										<div className="space-y-2">
											<div className="flex justify-between items-center">
												<div className="flex items-center gap-2">
													<div className="w-1.5 h-1.5 rounded-full bg-rose-500" />
													<span className="text-sm">Mercedes C300</span>
												</div>
												<span className="text-xs opacity-75">120 days</span>
											</div>
											<div className="flex justify-between items-center">
												<div className="flex items-center gap-2">
													<div className="w-1.5 h-1.5 rounded-full bg-rose-500" />
													<span className="text-sm">Audi Q7</span>
												</div>
												<span className="text-xs opacity-75">95 days</span>
											</div>
										</div>
									</div>

									<div className="col-span-2">
										<h4 className="text-sm font-medium mb-2">
											Inventory Age Distribution
										</h4>
										<div className="space-y-2">
											<div className="flex flex-col">
												<div className="flex justify-between text-xs mb-1">
													<span>0-30 days</span>
													<span className="font-medium">45%</span>
												</div>
												<Progress value={45} className="bg-white/10 h-1.5" />
											</div>
											<div className="flex flex-col">
												<div className="flex justify-between text-xs mb-1">
													<span>31-60 days</span>
													<span className="font-medium">30%</span>
												</div>
												<Progress value={30} className="bg-white/10 h-1.5" />
											</div>
											<div className="flex flex-col">
												<div className="flex justify-between text-xs mb-1">
													<span>60+ days</span>
													<span className="font-medium">25%</span>
												</div>
												<Progress value={25} className="bg-white/10 h-1.5" />
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</SignedIn>
				<SignedOut>
					<SignInButton />
				</SignedOut>
			</div>
		</div>
	);
}
