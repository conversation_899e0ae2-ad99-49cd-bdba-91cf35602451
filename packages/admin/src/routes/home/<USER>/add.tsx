import PartsForm from "@/components/forms/PartsForm";
import VehiclesForm from "@/components/forms/VehiclesForm";
import { createFileRoute } from "@tanstack/react-router";
import { fallback, zodValidator } from "@tanstack/zod-adapter";
import { z } from "zod";

const addInventorySchema = z.object({
	category: fallback(z.enum(["parts", "vehicles"]), "parts").default("parts"),
});

export const Route = createFileRoute("/home/<USER>/add")({
	validateSearch: zodValidator(addInventorySchema),
	component: RouteComponent,
});

function RouteComponent() {
	const { category } = Route.useSearch();

	return (
		<div className="container mx-auto py-6 px-6">
			<div className="w-full">
				<h2 className="text-2xl font-semibold mb-4 text-dms-500">
					{category === "parts" ? "Add New Parts" : "Add New Vehicles"}
				</h2>
				<p className="text-base leading-2 text-gray-950">
					Enter the details for the new{" "}
					{category === "parts" ? "part" : "vehicle"} to add to inventory.
				</p>
			</div>

			<div className="mt-4 w-full">
				{category === "parts" ? <PartsForm /> : <VehiclesForm />}
			</div>
		</div>
	);
}
