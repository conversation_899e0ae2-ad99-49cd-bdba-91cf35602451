import { AppSidebar } from "@/components/app-sidebar";
import { Input } from "@/components/ui/input";
// import {
// 	Breadcrumb,
// 	BreadcrumbList,
// 	BreadcrumbItem,
// 	BreadcrumbLink,
// 	BreadcrumbSeparator,
// 	BreadcrumbPage,
// } from "@/components/ui/breadcrumb";
import { Separator } from "@/components/ui/separator";
import {
	SidebarInset,
	SidebarProvider,
	SidebarTrigger,
} from "@/components/ui/sidebar";
import { Outlet, createFileRoute } from "@tanstack/react-router";
import { Search, X } from "lucide-react";
import { useState } from "react";

export const Route = createFileRoute("/home")({
	component: RouteComponent,
});

function RouteComponent() {
	const [searchQuery, setSearchQuery] = useState("");
	return (
		<SidebarProvider>
			<AppSidebar />
			<SidebarInset>
				<header className="flex h-16 shrink-0 items-center gap-2">
					<div className="flex items-center gap-2 px-4">
						<SidebarTrigger className="-ml-1" />
						<Separator orientation="vertical" className="mr-2 h-4" />
						{/* <Breadcrumb>
							<BreadcrumbList>
								<BreadcrumbItem className="hidden md:block">
									<BreadcrumbLink href="#">
										Building Your Application
									</BreadcrumbLink>
								</BreadcrumbItem>
								<BreadcrumbSeparator className="hidden md:block" />
								<BreadcrumbItem>
									<BreadcrumbPage>Data Fetching</BreadcrumbPage>
								</BreadcrumbItem>
							</BreadcrumbList>
						</Breadcrumb> */}
						<div className="relative">
							<Input
								placeholder="Search the application"
								className="w-80 pl-4 pr-10 rounded-full border-neutral-200"
								value={searchQuery}
								onChange={(e) => setSearchQuery(e.target.value)}
							/>
							{searchQuery ? (
								<button
									type="button"
									onClick={() => setSearchQuery("")}
									className="absolute right-10 top-1/2 transform -translate-y-1/2 text-neutral-400 hover:text-neutral-600"
								>
									<X className="h-4 w-4" />
								</button>
							) : null}
							<Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-dms-500 h-5 w-5" />
						</div>
					</div>
				</header>
				<Outlet />
			</SidebarInset>
		</SidebarProvider>
	);
}
