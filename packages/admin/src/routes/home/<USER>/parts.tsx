import SummaryCards from "@/components/generic/SummaryCards";
import { type ProductData, columns } from "@/components/parts/columns";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
	DropdownMenu,
	DropdownMenuCheckboxItem,
	DropdownMenuContent,
	DropdownMenuGroup,
	DropdownMenuItem,
	DropdownMenuLabel,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "@/components/ui/table";
import { products as initialProducts } from "@/data/products";
import { Link, createFileRoute } from "@tanstack/react-router";
import {
	type ColumnFiltersState,
	type FilterFn,
	flexRender,
	getCoreRowModel,
	getFilteredRowModel,
	useReactTable,
} from "@tanstack/react-table";
import {
	ChevronDown,
	DollarSign,
	Eye,
	MoreHorizontal,
	Package,
	Pencil,
	Plus,
	Search,
	Warehouse,
	X,
} from "lucide-react";
import { useMemo, useState } from "react";

// Define filter types
type FilterOption = {
	id: string;
	label: string;
	options: {
		value: string;
		label: string;
	}[];
};

// Define filter options
const filterOptions: FilterOption[] = [
	{
		id: "status",
		label: "Status",
		options: [
			{ value: "active", label: "Active" },
			{ value: "sold_out", label: "Sold out" },
			{ value: "low_stock", label: "Low in stock" },
		],
	},
	{
		id: "category",
		label: "Category",
		options: [
			{ value: "T-Shirt", label: "T-Shirt" },
			{ value: "T-shirts", label: "T-shirts" },
			{ value: "Tops", label: "Tops" },
			{ value: "Outwear", label: "Outwear" },
			{ value: "Accessories", label: "Accessories" },
			{ value: "Bottoms", label: "Bottoms" },
			{ value: "Dresses", label: "Dresses" },
		],
	},
	{
		id: "stock",
		label: "Stock",
		options: [
			{ value: "in_stock", label: "In Stock" },
			{ value: "out_of_stock", label: "Out of Stock" },
			{ value: "low", label: "Low Stock" },
		],
	},
];

// Global search filter function
const globalFilterFn: FilterFn<ProductData> = (row, _columnId, value) => {
	const searchValue = value.toLowerCase();

	// Search in name
	const nameValue = row.getValue("name");
	if (
		typeof nameValue === "string" &&
		nameValue.toLowerCase().includes(searchValue)
	) {
		return true;
	}

	// Search in category
	const categoryValue = row.getValue("category");
	if (
		typeof categoryValue === "string" &&
		categoryValue.toLowerCase().includes(searchValue)
	) {
		return true;
	}

	// Search in status (display value, not code)
	const status = row.getValue("status") as string;
	const statusDisplay =
		status === "active"
			? "active"
			: status === "sold_out"
				? "sold out"
				: "low in stock";

	if (statusDisplay.includes(searchValue)) {
		return true;
	}

	return false;
};

export const Route = createFileRoute("/home/<USER>/parts")({
	component: RouteComponent,
});

function RouteComponent() {
	const [data] = useState<ProductData[]>(initialProducts);
	const [searchQuery, setSearchQuery] = useState("");
	const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
	const [rowSelection, setRowSelection] = useState({});
	const [activeFilters, setActiveFilters] = useState<{
		[key: string]: string[];
	}>({});

	// Placeholder data for cards
	const partsStockData = {
		current: 1250,
		change: 5.2,
		isPositive: true,
	};

	const shippedPartsData = {
		total: 450,
		change: -3.1,
		isPositive: false,
	};

	const salesValueData = {
		total: 125000,
		change: 8.7,
		isPositive: true,
	};

	const inventoryValueData = {
		total: 275000,
		change: 2.3,
		isPositive: true,
	};
	const formattedData = [
		{
			title: "Parts Stock",
			value: partsStockData.current,
			trendingUp: partsStockData.isPositive,
			trendingValue: partsStockData.change,
			icon: <Package className="h-8 w-8 text-dms-500" />,
		},
		{
			title: "Shipped Parts",
			value: shippedPartsData.total,
			trendingUp: shippedPartsData.isPositive,
			trendingValue: shippedPartsData.change,
			icon: <Package className="h-8 w-8 text-dms-500" />,
		},
		{
			title: "Sales Value",
			value: salesValueData.total,
			trendingUp: salesValueData.isPositive,
			trendingValue: salesValueData.change,
			icon: <DollarSign className="h-8 w-8 text-dms-500" />,
		},
		{
			title: "Inventory Value",
			value: inventoryValueData.total,
			trendingUp: inventoryValueData.isPositive,
			trendingValue: inventoryValueData.change,
			icon: <Warehouse className="h-8 w-8 text-dms-500" />,
		},
	];

	// Apply filters to the table
	const applyFilter = (filterId: string, value: string, isActive: boolean) => {
		setActiveFilters((prev) => {
			const current = { ...prev };

			if (!current[filterId]) {
				current[filterId] = [];
			}

			if (isActive) {
				current[filterId] = [...current[filterId], value];
			} else {
				current[filterId] = current[filterId].filter((v) => v !== value);
			}

			// Remove empty filter arrays
			if (current[filterId].length === 0) {
				delete current[filterId];
			}

			return current;
		});
	};

	// Convert active filters to column filters for TanStack Table
	useMemo(() => {
		const newColumnFilters: ColumnFiltersState = [];

		// Process each active filter
		// biome-ignore lint/complexity/noForEach: <explanation>
		Object.entries(activeFilters).forEach(([filterId, values]) => {
			if (values.length > 0) {
				if (filterId === "stock") {
					// Special handling for stock filters
					newColumnFilters.push({
						id: "stock",
						value: values.map((v) => {
							if (v === "in_stock") return "in_stock";
							if (v === "out_of_stock") return "out_of_stock";
							return "low";
						}),
					});
				} else {
					// Standard column filter
					newColumnFilters.push({
						id: filterId,
						value: values,
					});
				}
			}
		});

		setColumnFilters(newColumnFilters);
	}, [activeFilters]);

	// Add actions column to the columns array
	const tableColumns = useMemo(
		() => [
			...columns,
			{
				id: "actions",
				header: "Actions",
				cell: () => {
					// const product = row.original;
					return (
						<DropdownMenu>
							<DropdownMenuTrigger asChild>
								<Button variant="ghost" size="icon" className="h-8 w-8">
									<MoreHorizontal className="h-4 w-4" />
									<span className="sr-only">Open menu</span>
								</Button>
							</DropdownMenuTrigger>
							<DropdownMenuContent align="end">
								<DropdownMenuItem>
									<Eye className="mr-2 h-4 w-4" />
									View
								</DropdownMenuItem>
								<DropdownMenuItem>
									<Pencil className="mr-2 h-4 w-4" />
									Update
								</DropdownMenuItem>
							</DropdownMenuContent>
						</DropdownMenu>
					);
				},
			},
		],
		[],
	);

	const table = useReactTable({
		data,
		columns: tableColumns,
		getCoreRowModel: getCoreRowModel(),
		getFilteredRowModel: getFilteredRowModel(),
		onColumnFiltersChange: setColumnFilters,
		onRowSelectionChange: setRowSelection,
		state: {
			columnFilters,
			globalFilter: searchQuery,
			rowSelection,
		},
		globalFilterFn,
		filterFns: {
			// Custom filter function for stock levels
			stock: (row, id, filterValues) => {
				const stock = row.getValue(id) as number;
				const filterValue = filterValues as string[];

				if (!filterValue.length) return true;

				if (filterValue.includes("in_stock") && stock > 1) return true;
				if (filterValue.includes("out_of_stock") && stock === 0) return true;
				if (filterValue.includes("low") && stock === 1) return true;

				return false;
			},
		},
	});

	// Remove a specific filter
	const removeFilter = (filterId: string, value: string) => {
		applyFilter(filterId, value, false);
	};

	// Clear all filters
	const clearAllFilters = () => {
		setActiveFilters({});
		setSearchQuery("");
	};

	// Check if a filter is active
	const isFilterActive = (filterId: string, value: string) => {
		return activeFilters[filterId]?.includes(value) || false;
	};

	// Count active filters
	const activeFilterCount = Object.values(activeFilters).reduce(
		(count, values) => count + values.length,
		0,
	);

	return (
		<div className="container mx-auto py-6 px-6 bg-gray-50">
			{/* New cards section */}
			<div className="grid grid-cols-4 gap-4 mb-8">
				{/* Parts Stock Card */}
				{formattedData.map((data, idx) => (
					<SummaryCards
						key={`${data.title}-${idx}`}
						title={data.title}
						value={data.value}
						trendingUp={data.trendingUp}
						trendingValue={data.trendingValue}
						icon={data.icon}
					/>
				))}
			</div>

			<div className="flex items-center justify-between mb-8">
				<div className="relative">
					<Input
						placeholder="Search"
						className="w-80 pl-4 pr-10 rounded-full border-neutral-200 bg-white"
						value={searchQuery}
						onChange={(e) => setSearchQuery(e.target.value)}
					/>
					{searchQuery ? (
						<button
							type="button"
							onClick={() => setSearchQuery("")}
							className="absolute right-10 top-1/2 transform -translate-y-1/2 text-neutral-400 hover:text-neutral-600"
						>
							<X className="h-4 w-4" />
						</button>
					) : null}
					<Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-dms-500 h-5 w-5" />
				</div>
				<div className="flex items-center gap-4">
					<DropdownMenu>
						<DropdownMenuTrigger asChild>
							<Button
								variant="outline"
								className="bg-dms-100 text-dms-700 border-none rounded-full px-6"
							>
								Filter by
								{activeFilterCount > 0 && (
									<Badge className="ml-2 bg-dms-600 text-white rounded-full h-5 w-5 p-0 flex items-center justify-center">
										{activeFilterCount}
									</Badge>
								)}
								<ChevronDown className="ml-2 h-4 w-4" />
							</Button>
						</DropdownMenuTrigger>
						<DropdownMenuContent align="end" className="w-56">
							{filterOptions.map((filterOption) => (
								<DropdownMenuGroup key={filterOption.id}>
									<DropdownMenuLabel>{filterOption.label}</DropdownMenuLabel>
									{filterOption.options.map((option) => (
										<DropdownMenuCheckboxItem
											key={`${filterOption.id}-${option.value}`}
											checked={isFilterActive(filterOption.id, option.value)}
											onCheckedChange={(checked) => {
												applyFilter(filterOption.id, option.value, checked);
											}}
										>
											{option.label}
										</DropdownMenuCheckboxItem>
									))}
									<DropdownMenuSeparator />
								</DropdownMenuGroup>
							))}
							{activeFilterCount > 0 && (
								<Button
									variant="ghost"
									className="w-full justify-center mt-2 text-sm"
									onClick={clearAllFilters}
								>
									Clear all filters
								</Button>
							)}
						</DropdownMenuContent>
					</DropdownMenu>
					<Button asChild className="rounded-full">
						<Link
							to="/home/<USER>/add"
							className="bg-dms-600 hover:bg-dms-700 rounded-full px-6"
						>
							<Plus className="mr-2 h-5 w-5" />
							Add Product
						</Link>
					</Button>
				</div>
			</div>

			{/* Active filters display */}
			{activeFilterCount > 0 && (
				<div className="flex flex-wrap gap-2 mb-4">
					{Object.entries(activeFilters).map(([filterId, values]) => {
						const filterOption = filterOptions.find((f) => f.id === filterId);

						return values.map((value) => {
							const option = filterOption?.options.find(
								(o) => o.value === value,
							);

							return (
								<Badge
									key={`${filterId}-${value}`}
									className="bg-dms-100 text-dms-700 hover:bg-dms-200 px-3 py-1 rounded-full"
								>
									{filterOption?.label}: {option?.label}
									<button
										type="button"
										className="ml-2"
										onClick={() => removeFilter(filterId, value)}
									>
										<X className="h-3 w-3" />
									</button>
								</Badge>
							);
						});
					})}

					{searchQuery && (
						<Badge className="bg-dms-100 text-dms-700 hover:bg-dms-200 px-3 py-1 rounded-full">
							Search: {searchQuery}
							<button
								type="button"
								className="ml-2"
								onClick={() => setSearchQuery("")}
							>
								<X className="h-3 w-3" />
							</button>
						</Badge>
					)}

					<Button
						variant="ghost"
						className="text-sm h-7 px-2 text-dms-700"
						onClick={clearAllFilters}
					>
						Clear all
					</Button>
				</div>
			)}

			<div className="rounded-md bg-neutral-50 py-4">
				<div className="rounded-md bg-white overflow-hidden">
					<Table className="table-container">
						<TableHeader>
							<TableRow className="bg-white border-b border-b-dms-100 shadow-none">
								<TableHead className="w-[50px]" />
								<TableHead className="w-[50px]" />
								<TableHead className="font-medium text-black">
									Name of product{" "}
									<ChevronDown className="inline-block ml-1 h-4 w-4 text-neutral-400" />
								</TableHead>
								<TableHead className="font-medium text-black">
									Status{" "}
									<ChevronDown className="inline-block ml-1 h-4 w-4 text-neutral-400" />
								</TableHead>
								<TableHead className="font-medium text-black">
									Stock info{" "}
									<ChevronDown className="inline-block ml-1 h-4 w-4 text-neutral-400" />
								</TableHead>
								<TableHead className="font-medium text-black">
									Category{" "}
									<ChevronDown className="inline-block ml-1 h-4 w-4 text-neutral-400" />
								</TableHead>
								<TableHead className="font-medium text-black">
									Location{" "}
									<ChevronDown className="inline-block ml-1 h-4 w-4 text-neutral-400" />
								</TableHead>
								<TableHead className="font-medium text-black">
									Actions
								</TableHead>
							</TableRow>
						</TableHeader>
						<TableBody>
							{table.getRowModel().rows.length > 0 ? (
								table.getRowModel().rows.map((row) => (
									<TableRow
										key={row.id}
										className={`hover:bg-dms-50 transition-all duration-200 ${row.getIsSelected() ? "bg-dms-50" : ""}`}
									>
										{row.getVisibleCells().map((cell) => (
											<TableCell key={cell.id} className="py-5">
												{flexRender(
													cell.column.columnDef.cell,
													cell.getContext(),
												)}
											</TableCell>
										))}
									</TableRow>
								))
							) : (
								<TableRow className="shadow-none" style={{ boxShadow: "none" }}>
									<TableCell colSpan={7} className="h-24 text-center">
										No results found.
									</TableCell>
								</TableRow>
							)}
						</TableBody>
					</Table>
				</div>
			</div>
		</div>
	);
}
