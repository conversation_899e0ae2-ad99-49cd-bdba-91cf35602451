import { isValid } from "ulidx";
import { z } from "zod";

export const DateTimeStampType = z.date();

const PartsStatus = z.enum(
	[
		"IN_STOCK", // Part is available in inventory
		"OUT_OF_STOCK", // Part is not available
		"LOW_STOCK", // Part is low in stock
		"ORDERED", // Part has been ordered but not received
		"IN_TRANSIT", // Part is being shipped to the dealership
		"DEFECTIVE", // Part is defective and unusable
		"RESERVED", // Part is reserved for a specific service job
		"INSTALLED", // Part has been installed on a vehicle
		"RETURNED", // Part was returned to the supplier
		"DISCONTINUED", // Part is no longer available for purchase
	],
	{ message: "Please provide a valid option" },
);

const PartsCategory = z.enum(
	[
		"ENGINE", // Engine components (e.g., filters, belts, gaskets)
		"TRANSMISSION", // Transmission and drivetrain parts
		"BRAKES", // Brake system components (e.g., pads, rotors)
		"SUSPENSION", // Suspension and steering parts
		"TIRES", // Tires and wheels
		"ELECTRICAL", // Electrical components (e.g., alternators, batteries)
		"EXHAUST", // Exhaust system parts (e.g., mufflers, catalytic converters)
		"BODY", // Exterior body parts (e.g., bumpers, mirrors)
		"INTERIOR", // Interior components (e.g., seats, dashboards)
		"FUEL", // Fuel system components (e.g., pumps, injectors)
		"COOLING", // Cooling system components (e.g., radiators, hoses)
		"LIGHTING", // Lighting components (e.g., headlamps, tail lamps)
		"ACCESSORIES", // Add-ons or optional accessories (e.g., floor mats)
		"TOOLS", // Tools for repairs or maintenance
		"GENERAL_MAINTENANCE", // General maintenance items (e.g., fluids, filters)
	],
	{ message: "Category is not available" },
);

export const categoryToUnits: Record<
	z.infer<typeof PartsCategory>,
	string[]
> = {
	ENGINE: ["pcs"],
	TRANSMISSION: ["pcs"],
	BRAKES: ["pcs"],
	SUSPENSION: ["pcs"],
	TIRES: ["pcs"],
	ELECTRICAL: ["pcs"],
	EXHAUST: ["pcs"],
	BODY: ["pcs"],
	INTERIOR: ["pcs"],
	FUEL: ["litres", "gallons"],
	COOLING: ["litres", "gallons"],
	LIGHTING: ["pcs"],
	ACCESSORIES: ["pcs"],
	TOOLS: ["pcs"],
	GENERAL_MAINTENANCE: ["litres", "gallons", "oz", "pcs"],
};

export const PartsSchema = z
	.object({
		name: z.string({ required_error: "Missing name property" }).min(1),
		status: PartsStatus,
		category: PartsCategory,
		quantity: z.number().positive(),
		minQuantity: z.number().positive(),
		costPerUnit: z
			.number({ required_error: "Missing costPerUnit property" })
			.positive({ message: "Provide a positive value" }),
		unit: z.string({ required_error: "Missing unit property" }),
		supplier: z
			.string()
			.optional()
			.refine((val) => val !== undefined && isValid(val), {
				message: "Please provide a valid supplier ID",
			}),
		vehicleCompatibility: z.array(z.string()).optional(),
		updatedAt: DateTimeStampType.optional(),
		createdAt: DateTimeStampType.optional(),
		sku: z.string({ required_error: "Stock Keeping Unit (SKU) is required" }),
		warranty: z.string().optional(),
		notes: z.string().optional(),
	})
	.refine(
		(data) => {
			const itemCategory = data.category;
			const validUnits = categoryToUnits[itemCategory] || [];
			return validUnits.includes(data.unit);
		},
		{
			message: "Invalid unit for selected category",
		},
	);

export const SupplierSchema = z.object({
	name: z
		.string()
		.min(2, { message: "Name must be at least 2 characters long" }),
	email: z.string().email({ message: "Invalid email address" }),
	phone: z
		.string()
		.min(10, { message: "Phone number must be at least 10 characters long" }),
	address: z
		.string()
		.min(5, { message: "Address must be at least 5 characters long" }),
	website: z.string().url({ message: "Invalid website URL" }).optional(),
	notes: z.string().optional(),
	contactName: z
		.string()
		.min(2, { message: "Contact name must be at least 2 characters long" }),
});

export type PartsType = z.infer<typeof PartsSchema>;
export type SupplierType = z.infer<typeof SupplierSchema>;
export type PartsCategoryType = z.infer<typeof PartsCategory>;
export type PartsStatusType = z.infer<typeof PartsStatus>;

// Category display names for better readability
export const categoryDisplayNames: Record<
	z.infer<typeof PartsCategory>,
	string
> = {
	ENGINE: "Engine",
	TRANSMISSION: "Transmission",
	BRAKES: "Brakes",
	SUSPENSION: "Suspension",
	TIRES: "Tires",
	ELECTRICAL: "Electrical",
	EXHAUST: "Exhaust",
	BODY: "Body",
	INTERIOR: "Interior",
	FUEL: "Fuel",
	COOLING: "Cooling",
	LIGHTING: "Lighting",
	ACCESSORIES: "Accessories",
	TOOLS: "Tools",
	GENERAL_MAINTENANCE: "General Maintenance",
};

// Status display names for better readability
export const statusDisplayNames: Record<z.infer<typeof PartsStatus>, string> = {
	IN_STOCK: "In Stock",
	OUT_OF_STOCK: "Out of Stock",
	LOW_STOCK: "Low Stock",
	ORDERED: "Ordered",
	IN_TRANSIT: "In Transit",
	DEFECTIVE: "Defective",
	RESERVED: "Reserved",
	INSTALLED: "Installed",
	RETURNED: "Returned",
	DISCONTINUED: "Discontinued",
};
