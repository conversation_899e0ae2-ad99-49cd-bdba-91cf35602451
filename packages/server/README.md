# DMS Core

## Pre-requisites
- [AWS CLI](https://docs.aws.amazon.com/cli/latest/userguide/getting-started-install.html)
- [Node.js (>= 18.x)](https://nodejs.org/)
- [Git](https://git-scm.com/)
- Ensure AWS credentials and config are set up:
  - Create or edit the `~/.aws/config` file:
    ```ini
    [sso-session your-aws-account-name]
    sso_start_url = https://your-portal-url/start
    sso_region = us-east-1

    [profile your-aws-account-name-dev]
    sso_session = your-aws-account-name
    sso_account_id = your-profile-id
    sso_role_name = AdministratorAccess
    region = us-east-1

    [profile your-aws-account-name-prod]
    sso_session = your-aws-account-name
    sso_account_id = your-profile-id
    sso_role_name = AdministratorAccess
    region = us-east-1
    ```
  - Create or edit the `~/.aws/credentials` file:
    ```ini
    [your-aws-account-name-dev]
    aws_access_key_id=your-access-key-id-from-the-aws-console
    aws_secret_access_key=your-secret-access-key-from-the-aws-console
    aws_session_token=your-session-token-from-the-aws-console

    [your-aws-account-name-prod]
    aws_access_key_id=your-access-key-id-from-the-aws-console
    aws_secret_access_key=your-secret-access-key-from-the-aws-console
    aws_session_token=your-session-token-from-the-aws-console
    ```
- Once the above have been setup, run `aws sso login --sso-session=your-aws-account-name` to login via your cli. This will authenticate your CLI to access your aws account.

## Setup & Installation
1. Clone the repository:

2. Navigate to the project directory:
  ```sh
  cd dms-core
  ```
3. Install dependencies:
  ```sh
  npm install
  ```

## Development
1. Start the development server:
  ```sh
  npm run local:dev
  ```

2. Take note of endpoint url generated when the application is successfully deployed. It will look something like this:  `https://somerandomstringofcharacters.lambda-url.us-east-1.on.aws/`

3. The above generated endpoint will be used in your frontend requests or your Postman requests.
### Environment Vars and Secrets
To add environment variables and/or secrets, follow these steps:

1. In the terminal, inside the project dir run
```sh
npx sst secret set YOUR_SECRET_KEY yoursecretkeyvalue
```
2. Load your secret key inside the `src/env/index.ts` file. There are examples there to follow.

4. Access these variables in your code using `Resource.AppSecrets.secretKey`. See `src/index.ts`, `src/core/users.ts` for examples

5. Remember to restart the dev server anytime a new secret/env var is added




## Deployment
Instructions coming soon

