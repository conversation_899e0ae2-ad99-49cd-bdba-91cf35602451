/* This file is auto-generated by SST. Do not edit. */
/* tslint:disable */
/* eslint-disable */
/* deno-fmt-ignore-file */
import "sst"
export {}
declare module "sst" {
  export interface Resource {
    "AppSecrets": {
      "clerkJwtAudience": string
      "clerkJwtKey": string
      "clerkPublishableKey": string
      "clerkSecretKey": string
      "type": "sst.sst.Linkable"
    }
    "CLERK_JWT_AUDIENCE": {
      "type": "sst.sst.Secret"
      "value": string
    }
    "CLERK_JWT_KEY": {
      "type": "sst.sst.Secret"
      "value": string
    }
    "CLERK_PUBLISHABLE_KEY": {
      "type": "sst.sst.Secret"
      "value": string
    }
    "CLERK_SECRET_KEY": {
      "type": "sst.sst.Secret"
      "value": string
    }
    "DMSCoreData": {
      "name": string
      "type": "sst.aws.Dynamo"
    }
    "DMSCoreFunction": {
      "name": string
      "type": "sst.aws.Function"
      "url": string
    }
    "adminDashboard": {
      "type": "sst.aws.StaticSite"
      "url": string
    }
    "dms_storage": {
      "name": string
      "type": "sst.aws.Bucket"
    }
  }
}
