{"name": "@dmscore-services/server", "type": "module", "scripts": {"build": "esbuild --bundle --outfile=./dist/index.js --platform=node --target=node20 ./src/index.ts", "deploy": "run-s build zip update", "update": "aws lambda update-function-code --zip-file fileb://lambda.zip --function-name hello", "zip": "zip -j lambda.zip dist/index.js", "format": "npx @biomejs/biome format --write --verbose ./src", "lint": "npx @biomejs/biome lint --write --verbose ./src", "dev": "sst dev"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@types/aws-lambda": "8.10.146", "@types/node": "^22.10.2", "esbuild": "^0.21.4", "npm-run-all2": "^6.2.0"}, "dependencies": {"@aws-sdk/client-dynamodb": "^3.751.0", "@aws-sdk/client-s3": "^3.750.0", "@aws-sdk/lib-dynamodb": "^3.751.0", "@clerk/backend": "^1.21.4", "@hono/clerk-auth": "^2.0.0", "@hono/zod-validator": "^0.4.2", "hono": "^4.6.14", "hono-pino": "^0.7.0", "pino": "^9.6.0", "sst": "*", "ulidx": "^2.4.1", "zod": "^3.24.1"}}