import type {
	GetItemCommandInput,
	PutItemCommandInput,
	QueryCommandInput,
	UpdateItemCommandInput,
} from "@aws-sdk/client-dynamodb";
import type { ScanCommandInput } from "@aws-sdk/lib-dynamodb";
import { Resource } from "sst";
import type { z } from "zod";
import type { NewVehicleSchema } from "../core/schemas/vehicle-schema";

type NewVehicle = z.infer<typeof NewVehicleSchema>;

export const getSingleVehicleById = (id: string): GetItemCommandInput => {
	const queryPayload: GetItemCommandInput = {
		TableName: Resource.DMSCoreData.name,
		Key: {
			PK: { S: `VEHICLE#${id}` },
			SK: { S: `DETAILS#${id}` },
		},
		// ProjectionExpression:
		// 	"#Make, #Model, #Vin, #Price, #Status, #Category, #Condition, #Year, #Mileage, #Transmission, #DriveTrain",
		// ExpressionAttributeNames: {
		// 	"#Make": "mk",
		// 	"#Model": "mo",
		// 	"#Vin": "vin",
		// 	"#Price": "pc",
		// 	"#Status": "st",
		// 	"#Category": "ct",
		// 	"#Condition": "cnd",
		// 	"#Year": "yr",
		// 	"#Mileage": "mlg",
		// 	"#Transmission": "tms",
		// 	"#DriveTrain": "dt",
		// },
	};
	return queryPayload;
};

export const getAllVehicles = (limit: number | undefined): ScanCommandInput => {
	const queryPayload: ScanCommandInput = {
		TableName: Resource.DMSCoreData.name,
		IndexName: "ETI",
		ProjectionExpression:
			"#Id, #Make, #Model, #Vin, #Price, #Status, #Condition, #Category, #Year, #Mileage, #Transmission, #DriveTrain",
		ExpressionAttributeNames: {
			"#Id": "id",
			"#Make": "mk",
			"#Model": "mo",
			"#Vin": "vin",
			"#Price": "pc",
			"#Status": "st",
			"#Condition": "cnd",
			"#Category": "ct",
			"#Year": "yr",
			"#Mileage": "mlg",
			"#Transmission": "tms",
			"#DriveTrain": "dt",
		},
		Limit: limit ? limit : 10,
	};
	return queryPayload;
};

// Based on our access patterns we want to be
// by secondary indexing using the following pattern
// PK: MAKE#<vehicleMake>
// SK: VEHICLE#ID
export const queryVehiclesByMake = ({
	make,
	limit,
}: { make: string; limit: number | undefined }): QueryCommandInput => {
	const queryPayload: QueryCommandInput = {
		TableName: Resource.DMSCoreData.name,
		IndexName: "GSI1",
		KeyConditionExpression:
			"GSI1PK = :partitionKey AND begins_with(PK, :rangeKey)",
		ProjectionExpression:
			"#Id, #Make, #Model, #Vin, #Price, #Status, #Condition, #Category, #Year, #Mileage, #Transmission, #DriveTrain",
		ExpressionAttributeNames: {
			"#Id": "id",
			"#Make": "mk",
			"#Model": "mo",
			"#Vin": "vin",
			"#Price": "pc",
			"#Status": "st",
			"#Condition": "cnd",
			"#Category": "ct",
			"#Year": "yr",
			"#Mileage": "mlg",
			"#Transmission": "tms",
			"#DriveTrain": "dt",
		},
		ExpressionAttributeValues: {
			":partitionKey": { S: `MAKE#${make}` },
			":rangeKey": { S: "VEHICLE#" },
		},
		ScanIndexForward: false,
		Limit: limit ? limit : 10,
	};

	return queryPayload;
};

export const queryByPriceRange = ({
	make,
	startPrice,
	endPrice,
	limit,
}: {
	make: string;
	startPrice: number;
	endPrice: number;
	limit: number | undefined;
}) => {
	const queryPayload: QueryCommandInput = {
		TableName: Resource.DMSCoreData.name,
		IndexName: "GSI2",
		KeyConditionExpression:
			"#PK = :hashKey AND #SK BETWEEN :startprice AND :endprice",
		ExpressionAttributeNames: {
			"#PK": "GSI1PK",
			"#SK": "GSI2SK",
		},
		ExpressionAttributeValues: {
			":hashKey": { S: `MAKE#${make}` },
			":startprice": { S: `PRICE#${startPrice}` },
			":endprice": { S: `PRICE#${endPrice}` },
		},
		Limit: limit ? limit : 25,
	};

	return queryPayload;
};

export const addNewVehicleToStore = (
	payload: NewVehicle,
	id: string,
): PutItemCommandInput => {
	const dynamoVehiclePayload: PutItemCommandInput = {
		TableName: Resource.DMSCoreData.name,
		Item: {
			PK: { S: `VEHICLE#${id}`.toString() },
			SK: { S: `DETAILS#${id}`.toString() },
			et: { S: "VEHICLE" },
			id: { S: `${id}`.toString() },
			mo: { S: `${payload.model}`.toString() },
			mk: { S: `${payload.make}`.toString() },
			st: { S: `${payload.status}`.toString() },
			cat: { N: `${Date.now()}`.toString() },
			yr: { S: `${payload.year}`.toString() },
			mlg: { N: `${payload.mileage}`.toString() },
			cnd: { S: `${payload.condition}`.toString() },
			ct: { S: `${payload.category}` },
			pc: { N: `${payload.price}`.toString() },
			vin: { S: `${payload.vin}`.toString() },
			dt: { S: `${payload.driveTrain}`.toString() },
			tms: { S: `${payload.transmission.type}` },
			tm: { S: `${payload.trim}`.toString() },
			eng: {
				M: {
					typ: { S: `${payload.engine.type}`.toString() },
					cpc: { N: `${payload.engine.capacity}`.toString() },
					hp: { N: `${payload.engine.horsepower}`.toString() },
					tq: { N: `${payload.engine.torque}`.toString() },
				},
			},
			GSI1PK: { S: `MAKE#${payload.make}`.toString() },
			GSI1SK: { S: `CONDITION#${payload.condition}#STATUS#${payload.status}` },
			GSI2SK: { S: `PRICE#${payload.price}`.toString() },
		},
	};
	if (payload.dimensions && dynamoVehiclePayload.Item) {
		dynamoVehiclePayload.Item.dmn = {
			M: {
				ln: { N: `${payload.dimensions?.length}`.toString() },
				wd: { N: `${payload.dimensions?.width}`.toString() },
				ht: { N: `${payload.dimensions?.height}`.toString() },
				wb: { N: `${payload.dimensions?.wheelbase}`.toString() },
			},
		};
	}
	if (payload.suspension && dynamoVehiclePayload.Item) {
		dynamoVehiclePayload.Item.sps = {
			M: {
				front: { S: `${payload.suspension?.front}`.toString() },
				rear: { S: `${payload.suspension?.rear}`.toString() },
			},
		};
	}
	return dynamoVehiclePayload;
};

export const updateVehicleStatusById = (
	id: string,
	payload: Partial<NewVehicle>,
): UpdateItemCommandInput => {
	const dynamoVehicleUpdatePayload: UpdateItemCommandInput = {
		TableName: Resource.DMSCoreData.name,
		Key: {
			PK: { S: `VEHICLE#${id}`.toString() },
			SK: { S: `DETAILS#${id}`.toString() },
		},
		ReturnValues: "UPDATED_NEW",
		UpdateExpression: "SET #STATUS = :new_status, #UpdatedAt = :updated_at",
		ExpressionAttributeNames: {
			"#STATUS": "vst",
			"#UpdatedAt": "uat",
		},
		ExpressionAttributeValues: {
			":new_status": { S: `${payload.status}`.toString() },
			":updated_at": { N: `${Date.now()}`.toString() },
		},
	};
	return dynamoVehicleUpdatePayload;
};
