import type {
	GetItemCommandInput,
	PutItemCommandInput,
	QueryCommandInput,
	ScanCommandInput,
	UpdateItemCommandInput,
} from "@aws-sdk/client-dynamodb";
import { Resource } from "sst";
import type { z } from "zod";
import type { PartsSchema } from "../core/schemas/parts-schema";

import { ulid } from "ulidx";
ulid();

type NewPart = z.infer<typeof PartsSchema>;

export const getPartsBySupplier = ({
	supplier,
	limit,
}: { supplier: string; limit: number }): QueryCommandInput => {
	const queryPayload: QueryCommandInput = {
		TableName: Resource.DMSCoreData.name,
		IndexName: "GSI1",
		KeyConditionExpression: "#PK = :supplier AND begins_with(#SK, :part)",
		ExpressionAttributeNames: {
			"#PK": "GSI1PK",
			"#SK": "GSI1SK",
		},
		ExpressionAttributeValues: {
			":supplier": { S: `SUPPLIER#${supplier}` },
			":part": { S: "PART#" },
		},
		Limit: limit,
	};
	return queryPayload;
};

export const getPartsBySupplierWithPriceRange = ({
	supplier,
	startPrice,
	endPrice,
	limit,
}: {
	supplier: string;
	startPrice: number;
	endPrice: number;
	limit: number | undefined;
}): QueryCommandInput => {
	const queryPayload: QueryCommandInput = {
		TableName: Resource.DMSCoreData.name,
		IndexName: "GSI2",
		KeyConditionExpression: "#PK = :supplier AND #SK BETWEEN :start AND :end",
		ExpressionAttributeNames: {
			"#PK": "GSI1PK",
			"#SK": "GSI2SK",
		},
		ExpressionAttributeValues: {
			":supplier": { S: `SUPPLIER#${supplier}` },
			":start": { S: `PRICE#${startPrice}` },
			":end": { S: `PRICE#${endPrice}` },
		},
		Limit: limit ? limit : 25,
	};
	return queryPayload;
};

export const getAllParts = (limit: number | undefined): ScanCommandInput => {
	const queryPayload: ScanCommandInput = {
		TableName: Resource.DMSCoreData.name,
		IndexName: "ETI",
		FilterExpression: "#Type = :entityType",
		ProjectionExpression:
			"#Id, #Name, #Supplier, #Status, #Price, #Quantity, #QuantityThreshold, #DateAdded",
		ExpressionAttributeNames: {
			"#Type": "et",
			"#Id": "id",
			"#Name": "name",
			"#Supplier": "sup",
			"#Status": "st",
			"#Price": "cpu",
			"#Quantity": "qty",
			"#QuantityThreshold": "mqt",
			"#DateAdded": "cat",
		},
		ExpressionAttributeValues: {
			":entityType": { S: "Part" },
		},
		Limit: limit ? limit : 10,
	};
	return queryPayload;
};

export const getSinglePartById = (id: string): GetItemCommandInput => {
	const queryPayload: GetItemCommandInput = {
		TableName: Resource.DMSCoreData.name,
		Key: {
			PK: { S: `PART#${id}` },
			SK: { S: `PART#${id}` },
		},
		ProjectionExpression:
			"#Id, #Name, #Status, #Quantity, #MinQuantity, #Supplier, #Category, #Unit, #CostPerUnit, #VehicleCompatibility, #Warranty, #Notes, #CreatedAt, #SKU",
		ExpressionAttributeNames: {
			"#Id": "id",
			"#Name": "nm",
			"#Status": "st",
			"#Quantity": "qty",
			"#MinQuantity": "mqt",
			"#Supplier": "sup",
			"#Category": "ct",
			"#Unit": "ut",
			"#CostPerUnit": "cpu",
			"#VehicleCompatibility": "vcp",
			"#Warranty": "wrt",
			"#Notes": "nts",
			"#CreatedAt": "cat",
			"#SKU": "sku",
		},
	};
	return queryPayload;
};

export const addNewPart = (payload: NewPart, id: string): PutItemCommandInput => {
	const dynamoPartPayload: PutItemCommandInput = {
		TableName: Resource.DMSCoreData.name,
		Item: {
			PK: { S: `PART#${id}`.toString() },
			SK: { S: `PART#${id}`.toString() },
			et: { S: "PART" },
			id: { S: `${id}`.toString() },
			nm: { S: `${payload.name}`.toString() },
			st: { S: `${payload.status}`.toString() },
			qty: { N: `${payload.quantity}`.toString() },
			mqt: { N: `${payload.minQuantity}`.toString() },
			sup: { S: `${payload.supplier}`.toString() },
			cat: { N: `${Date.now()}`.toString() },
			sku: { S: `${payload.sku}`.toString() },
			cpu: { N: `${payload.costPerUnit}`.toString() },
			ut: { S: `${payload.unit}`.toString() },
			ct: { S: `${payload.category}`.toString() },
			vcp: { SS: payload.vehicleCompatibility ?? [] },
			wrt: { S: `${payload.warranty}`.toString() },
			nts: { S: `${payload.notes}`.toString() },
			GSI1PK: { S: `SUPPLIER#${payload.supplier}` },
			GSI1SK: { S: `PART#${id}` },
			GSI2SK: { S: `PRICE#${payload.costPerUnit}` },
		},
	};
	return dynamoPartPayload;
};

export const updatePartById = ({
	id,
	payload,
}: {
	id: string;
	payload: Partial<NewPart>;
}): UpdateItemCommandInput => {
	const dynamoPartUpdatePayload: UpdateItemCommandInput = {
		TableName: Resource.DMSCoreData.name,
		Key: {
			PK: { S: `PART#${id}` },
			SK: { S: `PART#${id}` },
		},
		ReturnValues: "UPDATED_NEW",
		UpdateExpression:
			"SET #Status = :status, #Quantity = :quantity, #MinQuantity = :minQuantity, #Supplier = :supplier, #CostPerUnit = :costPerUnit, #UpdatedAt = :updatedAt",
		ExpressionAttributeNames: {
			"#Status": "st",
			"#Quantity": "qty",
			"#MinQuantity": "mqt",
			"#Supplier": "sup",
			"#CostPerUnit": "cpu",
			"#UpdatedAt": "uat",
		},
		ExpressionAttributeValues: {
			":status": { S: `${payload.status}` },
			":quantity": { N: `${payload.quantity}`.toString() },
			":minQuantity": { N: `${payload.minQuantity}`.toString() },
			":supplier": { S: `${payload.supplier}` },
			":costPerUnit": { N: `${payload.costPerUnit}`.toString() },
			":updatedAt": { N: `${Date.now()}`.toString() },
		},
	};
	return dynamoPartUpdatePayload;
};
