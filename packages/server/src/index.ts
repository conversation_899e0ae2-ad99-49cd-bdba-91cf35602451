import { Hono } from "hono";
import { pinoLogger } from "hono-pino";
import { handle } from "hono/aws-lambda";
import { cors } from "hono/cors";
import auth from "./core/auth";
// import sales from "./core/sales";
// import customers from "./core/customers";
import partsInventory from "./core/parts";
import suppliersService from "./core/suppliers";
import users from "./core/users";
import vehicles from "./core/vehicles";
import { verifyRequest } from "./utils/middleware";

const app = new Hono().basePath("/api");

app.use(
	"/api/*",
	cors({
		origin: ["http://localhost:5174"], // Allow all origins during development
		// credentials: true,
		// allowMethods: ["GET", "POST", "PUT", "DELETE", "PATCH"],
	}),
);

app.use(
	"*",
	pinoLogger({
		pino: {
			level: "info",
		},
	}),
);

// Define routes here
app.get("/", verifyRequest(), async (c) => {
	return c.json({ message: "Hello DMS Core user! Nice to have you :)" });
});

// AUTH Operations
app.route("/auth", auth);

// CRUD Operations for Users
app.route("/users", users);

// CRUD Operations for Vehicles
app.route("/vehicles", vehicles);

// Operations for Sales Workflow
// app.route("/api/sales", sales);

// Operations for customers
// app.route("/api/customers", customers);

// Parts Inventory
app.route("/parts", partsInventory);

// Suppliers
app.route("/suppliers", suppliersService);
// app.route('/departments', departments);
// app.route('/dashboard', dashboard);

export const handler = handle(app);
