import type { AttributeValue } from "@aws-sdk/client-dynamodb";
import { ulid } from "ulidx";

ulid();
Date.now();

export const transformSingleVehicleResponse = ({
	responseItem,
}: { responseItem: Record<string, AttributeValue> }) => {
	return {
		id: responseItem.id?.S,
		make: responseItem.mk?.S,
		model: responseItem.mo?.S,
		vin: responseItem.vin?.S,
		price: responseItem.pc?.N
			? Number.parseFloat(responseItem.pc.N)
			: undefined,
		status: responseItem.st?.S,
		category: responseItem.ct?.S,
		condition: responseItem.cnd?.S,
		year: responseItem.yr?.N
			? Number.parseInt(responseItem.yr.N, 10)
			: undefined,
		mileage: responseItem.mlg?.N
			? Number.parseFloat(responseItem.mlg.N)
			: undefined,
		transmission: responseItem.tms?.S,
		driveTrain: responseItem.dt?.S,
	};
};

export const transformVehiclesResponse = ({
	responseItem,
	responseCount,
	responseScannedCount,
}: {
	responseItem: Record<string, AttributeValue>[] | undefined;
	responseCount: number | undefined;
	responseScannedCount: number | undefined;
}) => {
	if (
		!responseItem ||
		responseItem.length === 0 ||
		!responseCount ||
		!responseScannedCount
	) {
		return {
			message: "No vehicles found",
			items: [],
			count: 0,
			scannedCount: 0,
		};
	}
	const mappedVehicleItems = responseItem.map((item) =>
		transformSingleVehicleResponse({ responseItem: item }),
	);

	return {
		message: "Vehicles fetched successfully",
		items: mappedVehicleItems,
		count: responseCount,
		scannedCount: responseScannedCount,
	};
};
