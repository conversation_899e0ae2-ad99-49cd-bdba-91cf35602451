import type {
	ConditionalCheckFailedException,
	InternalServerError,
	InvalidEndpointException,
	ItemCollectionSizeLimitExceededException,
	ProvisionedThroughputExceededException,
	ReplicatedWriteConflictException,
	RequestLimitExceeded,
	ResourceNotFoundException,
	TransactionConflictException,
} from "@aws-sdk/client-dynamodb";
import type { PinoLogger } from "hono-pino";
import { HTTPException } from "hono/http-exception";

export class AWSServiceErrors extends Error {
	constructor(
		public error:
			| ConditionalCheckFailedException
			| InvalidEndpointException
			| ItemCollectionSizeLimitExceededException
			| ProvisionedThroughputExceededException
			| ReplicatedWriteConflictException
			| RequestLimitExceeded
			| ResourceNotFoundException
			| InternalServerError
			| TransactionConflictException,
	) {
		super(error.message);
		this.name = error.name;
	}
}

export const authTokenErrorResponse = new Response("Unauthorized access", {
	status: 401,
	statusText: "Unauthorized: Missing or invalid token",
	headers: {
		Authenticate: "error=invalid-token",
	},
});

export const authUserIdErrorResponse = new Response("Unauthorized access", {
	status: 401,
	statusText: "Unauthorized: Missing user ID in token",
	headers: {
		Authenticate: "error=missing-user-id",
	},
});

export const authPermissionErrorResponse = new Response("Unauthorized access", {
	status: 401,
	statusText: "Forbidden: Insufficient access",
	headers: {
		Authenticate: "error=insufficient-access",
	},
});

export const badRequestErrorResponse = new Response("Bad Request", {
	status: 400,
	statusText: "Bad Request: Payload invalid",
});

export const transactionErrorResponse = new Response("Error with transaction", {
	status: 500,
	statusText: "Error processing data transaction.",
});

export const signInInvalidUserErrorResponse = new Response(
	"Unauthorized access",
	{
		status: 401,
		statusText: "Unauthorized: User not found",
		headers: {
			Authenticate: "error=user-not-found",
		},
	},
);

export const signInInvalidPasswordErrorResponse = new Response(
	"Unauthorized access",
	{
		status: 401,
		statusText: "Unauthorized: Invalid password",
		headers: {
			Authenticate: "error=invalid-password",
		},
	},
);

export const logErrorAndThrow = (logger: PinoLogger, err: Error) => {
	logger.error(
		{
			cause: err.cause,
			name: err.name,
			stack: err.stack,
		},
		err.message,
	);
	throw new HTTPException(500, { message: `${err.name} - ${err.message}` });
};
