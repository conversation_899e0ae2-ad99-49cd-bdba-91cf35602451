import { HTTPException } from "hono/http-exception";
import {
	authTokenErrorResponse,
	authUserIdErrorResponse,
} from "./errorResponses";
import type { Context, MiddlewareHandler } from "hono";
import { verifyToken } from "@clerk/backend";
import { Resource } from "sst";

// Middleware for Access Control
export const verifyRequest: () => MiddlewareHandler =
	() => async (c: Context, next) => {
		const dmscoreLogger = c.get("dmscorelogger");

		dmscoreLogger.info("Checking authorization...");

		const authHeader = c.req.header("Authorization");
		if (!authHeader || !authHeader.startsWith("Bearer ")) {
			dmscoreLogger.error("Token does not exist");
			throw new HTTPException(401, {
				res: authTokenErrorResponse,
			});
		}

		const token = authHeader.split(" ")[1];
		try {
			// Validate the token
			const claims = await verifyToken(token, {
				jwtKey: Resource.AppSecrets.clerkJwtKey,
				// audience: Resource.AppSecrets.clerkJwtAudience,
				authorizedParties: ["*"],
			});

			const userId = claims.sub;
			if (!userId) {
				dmscoreLogger.error("Invalid User ID in response");
				throw new HTTPException(401, {
					res: authUserIdErrorResponse,
				});
			}

			await next();
		} catch (error) {
			dmscoreLogger.error({ error }, "Error verifying user");
			throw new HTTPException(401, {
				res: authTokenErrorResponse,
			});
		}
	};
