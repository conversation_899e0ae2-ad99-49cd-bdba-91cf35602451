import { DynamoDBClient } from "@aws-sdk/client-dynamodb";
import { S3Client } from "@aws-sdk/client-s3";
import { DynamoDBDocumentClient } from "@aws-sdk/lib-dynamodb";
import { createClerkClient } from "@clerk/backend";
import { Resource } from "sst";

const clerkClient = createClerkClient({
	secretKey: Resource.AppSecrets.clerkSecretKey,
	publishableKey: Resource.AppSecrets.clerkPublishableKey,
});

const dbClient = new DynamoDBClient({
	region: "us-east-1",
});

const docClient = DynamoDBDocumentClient.from(dbClient, {
	marshallOptions: {
		convertEmptyValues: true,
		removeUndefinedValues: true,
	},
});

const s3Client = new S3Client({
	region: "us-east-1",
});

export { docClient, clerkClient, s3Client };
