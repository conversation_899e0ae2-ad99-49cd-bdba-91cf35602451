import {
	ConditionalCheckFailedException,
	DynamoDBClient,
	DynamoDBServiceException,
	PutItemCommand,
	type PutItemCommandInput,
	QueryCommand,
	UpdateItemCommand,
	type UpdateItemCommandInput,
} from "@aws-sdk/client-dynamodb";
import {
	DynamoDBDocumentClient,
	type QueryCommandInput,
	type PutCommandInput,
} from "@aws-sdk/lib-dynamodb";
import { clerkMiddleware } from "@hono/clerk-auth";
import { Hono } from "hono";
import { pinoLogger } from "hono-pino";
import { HTTPException } from "hono/http-exception";
import { Resource } from "sst";
import {
	AWSServiceErrors,
	logErrorAndThrow,
	transactionErrorResponse,
} from "../utils/errorResponses";
import { zValidator } from "@hono/zod-validator";
import {
	CustomerSchema,
	UpdateCustomerSchema,
} from "./schemas/customer-schema";
import { ulid } from "ulidx";
import {
	buildUpdateExpression,
	convertToDynamoDBItem,
} from "../utils/dbFunctions";
import { NewSaleRecordSchema } from "./schemas/sales-schema";

const dbClient = new DynamoDBClient({
	region: "us-east-1",
});
const docClient = DynamoDBDocumentClient.from(dbClient);

const customers = new Hono()
	.use(
		pinoLogger({
			pino: { level: "info" },
			contextKey: "customerLogger" as const,
		}),
	)
	.use(
		clerkMiddleware({
			secretKey: Resource.AppSecrets.clerkSecretKey,
			publishableKey: Resource.AppSecrets.clerkPublishableKey,
		}),
	)
	.get("/:customerId", async (c) => {
		const logger = c.get("customerLogger");
		const customerId = c.req.param("customerId");
		const queryParams: QueryCommandInput = {
			TableName: Resource.DMSCoreTable.name,
			Select: "ALL_ATTRIBUTES",
			KeyConditionExpression: "PK = :pk AND begins_with(SK, :sk)",
			ExpressionAttributeValues: {
				":pk": {
					S: `CUSTOMER#${customerId}`,
				},
				":sk": {
					S: "#METADATA",
				},
			},
			ConsistentRead: true,
		};
		try {
			const response = await docClient.send(new QueryCommand(queryParams));
			return c.json({
				success: true,
				data: response,
			});
		} catch (error) {
			if (
				error instanceof DynamoDBServiceException ||
				error instanceof ConditionalCheckFailedException
			) {
				logErrorAndThrow(logger, error);
			} else {
				logger.error({ error }, `error getting customer ${customerId}`);
				throw new HTTPException(500, { res: transactionErrorResponse });
			}
		}
	})
	.get("/", async (c) => {
		const logger = c.get("customerLogger");
		const queryLimit = c.req.query("limit");
		const queryParams: QueryCommandInput = {
			TableName: Resource.DMSCoreTable.name,
			IndexName: "TypeIndex",
			Select: "ALL_ATTRIBUTES",
			KeyConditionExpression: "ItemType = :itemtype",
			ExpressionAttributeValues: {
				":itemtype": { S: "Customer" },
			},
			Limit: queryLimit ? Number.parseInt(queryLimit, 10) : 10,
		};
		try {
			const response = await docClient.send(new QueryCommand(queryParams));
			return c.json({
				success: true,
				data: response,
			});
		} catch (error) {
			if (error instanceof DynamoDBServiceException) {
				logErrorAndThrow(logger, error);
			} else {
				logger.error({ error }, "error getting customers");
				throw new HTTPException(500, { res: transactionErrorResponse });
			}
		}
	})
	.post("/:id/sale", zValidator("json", NewSaleRecordSchema), async (c) => {
		const logger = c.get("customerLogger");
		const payload = c.req.valid("json");
		const customerId = c.req.param("id");
		if (customerId !== payload.customerId) {
			throw new HTTPException(400, { message: "Invalid data" });
		}
		const saleMadePayload: PutItemCommandInput = {
			TableName: Resource.DMSCoreTable.name,
			Item: convertToDynamoDBItem(payload, "Sale"),
		};
		const updateVehicleRecord: UpdateItemCommandInput = {
			TableName: Resource.DMSCoreTable.name,
			Key: {
				PK: { S: `VEHICLE#${payload.itemSold.vehicle?.id}` },
				SK: { S: "#METADATA" },
			},
			UpdateExpression:
				"SET #Data.#IsAvailable = :isAvailable, #Data.#VehicleStatus = :vehicleStatus",
			ExpressionAttributeNames: {
				"#Data": "Data",
				"#IsAvailable": "IsAvailable",
				"#VehicleStatus": "VehicleStatus",
			},
			ExpressionAttributeValues: {
				":isAvailable": { S: "Sold" },
				":vehicleStatus": { S: "sold" },
			},
			ReturnValues: "UPDATED_NEW",
		};
		try {
			await docClient.send(new PutItemCommand(saleMadePayload));
			await docClient.send(new UpdateItemCommand(updateVehicleRecord));
			return c.json({
				success: true,
				message: "Product sale successfully recorded",
			});
		} catch (error) {
			if (error instanceof AWSServiceErrors) {
				logErrorAndThrow(logger, error);
			} else {
				logger.error({ error }, "Error adding sale record");
				throw new HTTPException(500, {
					message: "Error adding sale record",
				});
			}
		}
	})
	.post("/", zValidator("json", CustomerSchema), async (c) => {
		const logger = c.get("customerLogger");
		const payload = c.req.valid("json");
		const custId = ulid();
		payload.customerId = custId;
		const customerPayload: PutCommandInput = {
			TableName: Resource.DMSCoreTable.name,
			Item: convertToDynamoDBItem(payload, "Customer"),
		};
		try {
			await docClient.send(new PutItemCommand(customerPayload));
			return c.json({
				success: true,
				message: `${custId} with name '${payload.firstName} ${payload.lastName}' has been saved`,
			});
		} catch (error) {
			if (error instanceof AWSServiceErrors) {
				logErrorAndThrow(logger, error);
			} else {
				logger.error({ error }, "error saving customer details");
				throw new HTTPException(500, { res: transactionErrorResponse });
			}
		}
	})
	.put("/:customerId", zValidator("json", UpdateCustomerSchema), async (c) => {
		const logger = c.get("customerLogger");
		const customerId = c.req.param("customerId");
		const payload = c.req.valid("json");
		const {
			updateExpressionParts,
			expressionAttributeNames,
			expressionAttributeValues,
		} = buildUpdateExpression(payload);
		const updateExpression = `SET ${updateExpressionParts.join(", ")}`;
		const updatePayload: UpdateItemCommandInput = {
			TableName: Resource.DMSCoreTable.name,
			Key: {
				PK: { S: `CUSTOMER#${customerId}` },
				SK: { S: "#METADATA" },
			},
			UpdateExpression: updateExpression,
			ExpressionAttributeNames: expressionAttributeNames,
			ExpressionAttributeValues: expressionAttributeValues,
			ReturnValues: "UPDATED_NEW",
		};
		try {
			const response = await docClient.send(
				new UpdateItemCommand(updatePayload),
			);
			return c.json({
				success: true,
				data: response,
			});
		} catch (error) {
			if (error instanceof AWSServiceErrors) {
				logErrorAndThrow(logger, error);
			} else {
				logger.error({ error }, "error saving customer details");
				throw new HTTPException(500, { res: transactionErrorResponse });
			}
		}
	});

export default customers;
