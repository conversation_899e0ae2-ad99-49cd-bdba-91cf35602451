import {
	DynamoDBClient,
	GetItemCommand,
	type GetItemCommandInput,
	PutItemCommand,
	type PutItemCommandInput,
	QueryCommand,
	UpdateItemCommand,
	type UpdateItemCommandInput,
} from "@aws-sdk/client-dynamodb";
import {
	DynamoDBDocumentClient,
	type QueryCommandInput,
} from "@aws-sdk/lib-dynamodb";
import { clerkMiddleware } from "@hono/clerk-auth";
import { Hono } from "hono";
import { pinoLogger } from "hono-pino";
import { HTTPException } from "hono/http-exception";
import { Resource } from "sst";
import {
	NewSaleRecordSchema,
	UpdateSaleRecordSchema,
} from "./schemas/sales-schema";
import {
	AWSServiceErrors,
	logErrorAndThrow,
	transactionErrorResponse,
} from "../utils/errorResponses";
import { zValidator } from "@hono/zod-validator";
import {
	buildUpdateExpression,
	convertToDynamoDBItem,
} from "../utils/dbFunctions";

const dbClient = new DynamoDBClient({
	region: "us-east-1",
});
const docClient = DynamoDBDocumentClient.from(dbClient);

const sales = new Hono()
	.use(
		pinoLogger({
			pino: { level: "info" },
			contextKey: "saleslogger" as const,
		}),
	)
	.use(
		clerkMiddleware({
			secretKey: Resource.AppSecrets.clerkSecretKey,
			publishableKey: Resource.AppSecrets.clerkPublishableKey,
		}),
	)
	.get("/:id", async (c) => {
		const logger = c.get("saleslogger");
		const salesId = c.req.param("id");
		const customerId = c.req.param("customerId");
		const getItemParams: GetItemCommandInput = {
			TableName: Resource.DMSCoreTable.name,
			Key: {
				SaleID: { S: `${salesId}` },
				CustomerID: { S: `${customerId}` },
			},
		};
		try {
			const response = await docClient.send(new GetItemCommand(getItemParams));
			return c.json({
				success: true,
				data: response,
			});
		} catch (error) {
			logger.error({ error }, "Error retrieving sales data");
			throw new HTTPException(404, {
				message: `Error retriving Sales Data with ID: ${salesId}`,
			});
		}
	})
	// GET all sales
	.get("/", async (c) => {
		const logger = c.get("saleslogger");
		const queryLimit = c.req.param("limit");
		const queryParams: QueryCommandInput = {
			TableName: Resource.DMSCoreTable.name,
			IndexName: "TypeIndex",
			Select: "ALL_ATTRIBUTES",
			KeyConditionExpression: "ItemType = :itemtype",
			ExpressionAttributeValues: {
				":itemtype": { S: "Sale" },
			},
			Limit: queryLimit ? Number.parseInt(queryLimit, 10) : 10,
		};
		try {
			const response = await docClient.send(new QueryCommand(queryParams));
			return c.json({
				success: true,
				data: response,
			});
		} catch (error) {
			logger.error({ error }, "Error getting all sales");
			throw new HTTPException(400, { message: "Error getting all sales" });
		}
	})
	.post("/", zValidator("json", NewSaleRecordSchema), async (c) => {
		const logger = c.get("saleslogger");
		const payload = c.req.valid("json");
		// generate PutCommand Payload for Sale Record
		const addNewRecordParams: PutItemCommandInput = {
			TableName: Resource.DMSCoreTable.name,
			Item: convertToDynamoDBItem(payload, "Sale"),
		};

		const updateVehicleRecord: UpdateItemCommandInput = {
			TableName: Resource.DMSCoreTable.name,
			Key: {
				PK: { S: `VEHICLE#${payload.itemSold.vehicle?.id}` },
				SK: { S: "#METADATA" },
			},
			UpdateExpression:
				"SET #Data.#IsAvailable = :isAvailable, #Data.#VehicleStatus = :vehicleStatus",
			ExpressionAttributeNames: {
				"#Data": "Data",
				"#IsAvailable": "IsAvailable",
				"#VehicleStatus": "VehicleStatus",
			},
			ExpressionAttributeValues: {
				":isAvailable": { S: "Sold" },
				":vehicleStatus": { S: "sold" },
			},
			ReturnValues: "UPDATED_NEW",
		};
		// Submit requests
		try {
			await docClient.send(new PutItemCommand(addNewRecordParams));
			await docClient.send(new UpdateItemCommand(updateVehicleRecord));
			return c.json({
				success: true,
				message: "Product sale successfully recorded",
			});
		} catch (error) {
			if (error instanceof AWSServiceErrors) {
				logger.info({ addNewRecordParams }, "request payload");
				logErrorAndThrow(logger, error);
			} else {
				logger.error({ error }, "error while creating records");
				throw new HTTPException(500, { res: transactionErrorResponse });
			}
		}
	})
	.put(
		"/:id/:customerId",
		zValidator("json", UpdateSaleRecordSchema),
		async (c) => {
			const logger = c.get("saleslogger");
			const saleId = c.req.param("id");
			const customerId = c.req.param("customerId");
			const payload = c.req.valid("json");
			const {
				updateExpressionParts,
				expressionAttributeNames,
				expressionAttributeValues,
			} = buildUpdateExpression(payload);
			const updateExpression = `SET ${updateExpressionParts.join(", ")}`;
			const updateInput: UpdateItemCommandInput = {
				TableName: Resource.DMSCoreTable.name,
				Key: {
					SaleID: { S: `${saleId}` },
					CustomerID: { N: `${customerId}` },
				},
				UpdateExpression: updateExpression,
				ExpressionAttributeNames: expressionAttributeNames,
				ExpressionAttributeValues: expressionAttributeValues,
				ReturnValues: "UPDATED_NEW",
			};
			try {
				const response = await docClient.send(
					new UpdateItemCommand(updateInput),
				);
				return c.json({
					success: true,
					data: response,
				});
			} catch (error) {
				logger.error({ error }, "Error updating sale record");
				throw new HTTPException(500, { res: transactionErrorResponse });
			}
		},
	);

export default sales;
