import { z } from "zod";
import { DateTimeStampType } from "./vehicle-schema";
import { isValid, ulid } from "ulidx";

const SaleStatus = z.enum(
	[
		"INITIATED", // Sale process has started but not completed
		"PENDING_APPROVAL", // Sale awaiting management or financing approval
		"APPROVED", // Sale approved and awaiting final payment
		"COMPLETED", // Sale fully completed and closed
		"CANCELLED", // Sale process was cancelled
		"REFUNDED", // Sale was refunded
	],
	{ message: "Provide a valid option" },
);

export const NewSaleRecordSchema = z.object({
	saleId: z
		.string()
		.default(() => ulid())
		.refine((val) => isValid(val), {
			message: "Invalid Sale ID provided",
		}),
	customerId: z.string().refine((val) => isValid(val), {
		message: "Invalid Customer ID provided",
	}),
	saleDate: DateTimeStampType,
	salesPerson: z
		.string()
		.default(() => ulid())
		.refine((val) => isValid(val), { message: "Invalid SalePersonnel" }),
	saleStatus: SaleStatus,
	itemSold: z.object({
		type: z.enum(["vehicle", "part"]),
		parts: z
			.array(
				z.object({
					partId: z.string().refine((val) => isValid(val), {
						message: "Invalid Part ID provided",
					}),
					quantityUsed: z.number(),
					cost: z.number().positive(),
				}),
			)
			.optional(),
		vehicle: z
			.object({
				id: z.string().refine((val) => isValid(val), {
					message: "Invalid Vehicle ID provided",
				}),
				cost: z.number().positive(),
			})
			.optional(),
	}),
	totalCost: z.number().positive(),
});

export const UpdateSaleRecordSchema = z.object({
	saleId: z
		.string({ required_error: "Provide a valid id to proceed" })
		.refine((val) => isValid(val), {
			message: "Invalid Sale ID provided",
		}),
	customerId: z.string().refine((val) => isValid(val), {
		message: "Invalid Customer ID provided",
	}),
	updatedAt: DateTimeStampType,
	salesPerson: z
		.string()
		.refine((val) => isValid(val), { message: "Invalid SalePersonnel" }),
	itemSold: z.object({
		type: z.enum(["vehicle", "part"]),
		parts: z
			.array(
				z.object({
					partId: z.string().refine((val) => isValid(val), {
						message: "Invalid Part ID provided",
					}),
					quantityUsed: z.number(),
					cost: z.number().positive(),
				}),
			)
			.optional(),
		vehicle: z
			.object({
				id: z.string().refine((val) => isValid(val), {
					message: "Invalid Vehicle ID provided",
				}),
				cost: z.number().positive(),
			})
			.optional(),
	}),
	totalCost: z.number().positive(),
});

export type SaleType = z.infer<typeof NewSaleRecordSchema>;
