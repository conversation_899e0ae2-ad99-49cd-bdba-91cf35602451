import { isValid, ulid } from "ulidx";
import { z } from "zod";

const ServiceType = z.enum(
	[
		"OIL_CHANGE", // Oil change service
		"TIRE_ROTATION", // Tire rotation
		"BRAKE_SERVICE", // Brake system maintenance
		"ENGINE_DIAGNOSTIC", // Engine diagnostics
		"SUSPENSION_SERVICE", // Suspension repair or maintenance
		"TRANSMISSION_SERVICE", // Transmission-related service
		"AIR_CONDITIONING", // Air conditioning repair or recharge
		"GENERAL_INSPECTION", // Comprehensive inspection
		"BATTERY_REPLACEMENT", // Battery service
		"ALIGNMENT", // Wheel alignment
		"FUEL_SYSTEM_CLEANING", // Cleaning or maintenance of fuel systems
		"CUSTOM",
	],
	{ message: "Please select a valid option" },
);

const ServiceStatus = z.enum([
	"SCHEDULED", // Service has been scheduled
	"IN_PROGRESS", // Service is currently being performed
	"COMPLETED", // Service is completed
	"CANCELLED", // Service was cancelled
	"PENDING", // Awaiting approval or parts
	"ON_HOLD", // Service is paused
]);

const VehicleStatus = z.enum(
	[
		"AVAILABLE", // Vehicle is available for sale
		"PENDING_SALE", // Vehicle has an interested buyer but the sale is not finalized
		"SOLD", // Vehicle has been sold
		"IN_TRANSIT", // Vehicle is being transported to the dealership
		"UNDER_MAINTENANCE", // Vehicle is undergoing maintenance or repairs
		"RESERVED", // Vehicle is reserved for a customer
		"TRADED_IN", // Vehicle has been traded in by a customer
		"OFF_LOT", // Vehicle is temporarily removed from the lot
		"RETIRED", // Vehicle has been removed from the inventory (e.g., scrapped or sent to auction)
		"AUCTION", // Vehicle is being prepared for or sent to auction
	],
	{ message: "Please provide a valid option" },
);

const VehicleCategory = z.enum(
	[
		"Sedan",
		"SUV",
		"Hatchback",
		"Truck",
		"Coupe",
		"Convertible",
		"Van",
		"Wagon",
		"Other",
	],
	{ message: "Please provide a valid category option" },
);

// Engine
const EngineSchema = z.object({
	type: z.enum(["Petrol", "Diesel", "Electric", "Hybrid", "Other"], {
		required_error: "Engine Type is required",
	}),
	capacity: z.number().positive({
		message: "Engine Capacity must be positive",
	}), // Capacity in liters
	horsepower: z.number().positive().optional(), // Horsepower
	torque: z.number().positive().optional(), // Torque in Nm
});

// Transmission
const TransmissionSchema = z.object({
	type: z.enum(["Manual", "Automatic", "CVT", "Semi-Automatic"], {
		required_error: "Transmission type is required",
		message: "Cannot proceed without transmission type",
	}),
});

// Suspension
const SuspensionSchema = z.object({
	front: z.string().optional(),
	rear: z.string().optional(),
});

// Dimensions
const DimensionsSchema = z.object({
	length: z.number().positive().optional(), // in mm
	width: z.number().positive().optional(), // in mm
	height: z.number().positive().optional(), // in mm
	wheelbase: z.number().positive().optional(), // in mm
});

// Location
const LocationSchema = z.object({
	country: z.string(),
	state: z.string(),
	city: z.string(),
	address: z.string().optional(),
});

// Images
const ImagesSchema = z.array(z.string().url()).optional();

export const DateTimeStampType = z
	.number()
	.refine((val) => !Number.isNaN(new Date(val).getTime()), {
		message: "Invalid timestamp",
	});

// Service History
export const ServiceHistorySchema = z
	.object({
		serviceId: z.string().optional(),
		vehicleId: z.string().optional(),
		serviceDate: DateTimeStampType, // ISO 8601
		notes: z.string(),
		technician: z.string(),
		mileageAtService: z.number().positive(),
		mileageAfterService: z.number().positive().optional(),
		servicedBy: z.string(),
		serviceType: ServiceType,
		serviceStatus: ServiceStatus,
		partsUsed: z
			.array(
				z.object({
					partId: z.string(),
					quantityUsed: z.number(),
					cost: z.number(),
				}),
			)
			.optional(),
		totalCost: z.number(),
	})
	.refine(
		(data) => {
			if (data.mileageAfterService) {
				return data.mileageAfterService < data.mileageAtService;
			}
		},
		{ message: "Value is invalid. Cannot be less than before" },
	);

export const NewVehicleSchema = z
	.object({
		branchId: z
			.string({ required_error: "Branch info is required" })
			.default(() => ulid())
			.refine((val) => isValid(val), { message: "Branch ID is invalid" }),
		mileage: z.number().default(0),
		year: z
			.number({
				required_error: `Please provide a valid year range from 1900 - ${new Date().getFullYear() + 1}`,
			})
			.min(1914, { message: "Sorry, this car is too old" })
			.max(new Date().getFullYear() + 1, {
				message: "Sorry, that is to far into the future",
			}),
		model: z.string({
			required_error: "Provide an appropriate Model for this vehicle",
		}),
		make: z.string({
			required_error: "Provide an appropriate Make for this Vehicle",
		}),
		condition: z.enum(["NEW", "USED", "CERTIFIED"]),
		status: VehicleStatus,
		price: z.number(),
		trim: z.string(),
		category: VehicleCategory,
		vin: z
			.string({ required_error: "VIN is required to add vehicle" })
			.length(17, "VIN must be exactly 17 characters")
			.regex(
				/^[A-HJ-NPR-Z0-9]*$/,
				"VIN must consist of alphanumeric characters without I, O, or Q",
			),
		engine: EngineSchema,
		driveTrain: z.enum([
			"Front Wheel Drive",
			"Rear Wheel Drive",
			"All Wheel Drive",
			"4x4",
		]),
		transmission: TransmissionSchema,
		suspension: SuspensionSchema.optional(),
		dimensions: DimensionsSchema.optional(),
		serviceHistory: z.array(ServiceHistorySchema).optional(),
		images: ImagesSchema.optional(),
		location: LocationSchema.optional(),
	})
	.refine(
		(data) => {
			if (data.condition === "USED" || data.condition === "CERTIFIED") {
				return typeof data.mileage === "number" && data.mileage >= 5;
			}
			return true;
		},
		{
			message: "Mileage is required and must be greater than 5",
			path: ["mileage"],
		},
	);

export const EditVehicleSchema = z.object({
	vehicleID: z.string({
		required_error: "ID is required for this vehicle",
	}),
	vin: z
		.string({ required_error: "VIN is required to add vehicle" })
		.length(17, "VIN must be exactly 17 characters")
		.regex(
			/^[A-HJ-NPR-Z0-9]*$/,
			"VIN must consist of alphanumeric characters without I, O, or Q",
		)
		.optional(),
	dealer: z
		.string({
			required_error: "Vehicle must be associated with a dealer",
			invalid_type_error: "Dealer value is incorrect",
		})
		.optional(),
	createdAt: z.number().optional(),
	updatedAt: z.number().optional(),
	year: z
		.number({
			required_error: `Please provide a valid year range from 1900 - ${new Date().getFullYear() + 1}`,
		})
		.min(1914, { message: "Sorry, this car is too old" })
		.max(new Date().getFullYear() + 1, {
			message: "Sorry, that is to far into the future",
		})
		.optional(),
	model: z
		.string({
			required_error: "Provide an appropriate Model for this vehicle",
		})
		.optional(),
	make: z
		.string({
			required_error: "Provide an appropriate Make for this Vehicle",
		})
		.optional(),
	vehicleCondition: z.enum(["brand new", "used"]).optional(),
	vehicleStatus: z
		.enum(["available", "sold"], {
			message: "Can only be available or sold",
		})
		.optional(),
	price: z.number().optional(),
	trim: z.string().optional(),
	class: z
		.enum([
			"Sedan",
			"SUV",
			"Hatchback",
			"Truck",
			"Coupe",
			"Convertible",
			"Van",
			"Wagon",
			"Other",
		])
		.optional(),
	engine: EngineSchema.optional(),
	driveTrain: z
		.enum(["Front Wheel Drive", "Rear Wheel Drive", "All Wheel Drive", "4x4"])
		.optional(),
	transmission: TransmissionSchema.optional(),
	suspension: SuspensionSchema.optional(),
	dimensions: DimensionsSchema.optional(),
	serviceHistory: z.array(ServiceHistorySchema).optional(),
	images: ImagesSchema.optional(),
	location: LocationSchema.optional(),
	insurancePolicy: z.string().optional(),
});

// USE THIS SCHEMA IF YOU WANT TO MAKE A SPECIFIC
// SEARCH BASED ON THE PARTITION KEY
export const QueryVehicleBySchema = z.object({
	partitionKey: z.string().min(1, "partitionKey is required"),
	sortKeyCondition: z.string().optional(),
	attribute: z.string().optional(),
	value: z.string().optional(),
	limit: z.preprocess(
		(val) => Number(val),
		z.number().int().positive().optional(),
	),
	exclusiveStartKey: z.preprocess(
		(val) => (val ? JSON.parse(val as string) : undefined),
		z.any().optional(),
	),
});

// USE THIS SCHEMA IF YOU WANT TO MAKE A DATABASE WIDE
// SEARCH / FILTER
export const ScanVehiclesSchema = z.object({
	attribute: z.string(),
	value: z.string(),
	limit: z.preprocess(
		(val) => Number(val),
		z.number().int().positive().optional(),
	),
	exclusiveStartKey: z.preprocess(
		(val) => (val ? JSON.parse(val as string) : undefined),
		z.any().optional(),
	),
});

export const ServicePartsSchema = z.object({
	name: z.string(),
	partId: z.string(),
	batchNumber: z.string(),
	manufacturer: z.string(),
	manufacturerPartNumber: z.string(),
	quantity: z.number(),
	description: z.string().optional(),
	category: z.string(),
});

export type VehicleType = z.infer<typeof NewVehicleSchema>;
export type ServiceHistoryType = z.infer<typeof ServiceHistorySchema>;
