import { z } from "zod";
import { DateTimeStampType } from "./vehicle-schema";
import { isValid } from "ulidx";

const CustomerServiceInfoSchema = z.object({
	serviceDate: z.number(),
	serviceType: z.string(),
	description: z.string(),
	attendingMechanic: z.string(),
	mechanicReport: z.string(),
	cost: z.number(),
});

const CustomerStatus = z.enum(
	[
		"ACTIVE", // Customer is active and engaged
		"INACTIVE", // Customer has not interacted recently
		"LEAD", // Potential customer, not yet converted
		"VIP", // High-value or repeat customer
		"BANNED", // Customer banned due to policy violations
		"DECEASED", // Customer marked as deceased
	],
	{ message: "Please provide a valid option" },
);

export const CustomerSchema = z.object({
	customerId: z
		.string()
		.refine((val) => isValid(val), { message: "Provide a valid ID" }),
	firstName: z.string({ required_error: "Customer first name is required" }),
	lastName: z.string({ required_error: "Customer last name is required" }),
	email: z
		.string({ required_error: "Customer email is required" })
		.email({ message: "Please provide a valid email address" }),
	phone: z.string({ required_error: "Customer phone number required" }),
	status: CustomerStatus,
	address: z
		.object({
			street: z.string().optional(),
			ghpost: z.string(),
			city: z.string().optional(),
			country: z.string(),
		})
		.optional(),
	vehicleId: z.string().optional(),
	createdAt: DateTimeStampType,
});

export const UpdateCustomerSchema = z.object({
	customer: CustomerSchema,
	serviceInfo: CustomerServiceInfoSchema.optional(),
});

export type CustomerType = z.infer<typeof CustomerSchema>;
