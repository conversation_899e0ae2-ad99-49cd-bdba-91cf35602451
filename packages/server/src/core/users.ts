import { clerkMiddleware } from "@hono/clerk-auth";
import { Hono } from "hono";
import { Resource } from "sst";
import { verifyRequest } from "../utils/middleware";
import { clerkClient } from "../utils/serviceClients";

const app = new Hono();
// protect all routes with clerk middleware
app
	.use(
		"*",
		clerkMiddleware({
			secretKey: Resource.AppSecrets.clerkSecretKey,
			publishableKey: Resource.AppSecrets.clerkPublishableKey,
		}),
	)
	// CRUD Operations for Users
	// CREATE User
	.post("/", async (c) => {
		const { username, email, password } = await c.req.json();
		const user = await clerkClient.users.createUser({
			emailAddress: [email],
			password: password,
			username,
		});
		return c.json(user);
	})
	// READ Users
	.get("/", async (c) => {
		const users = await clerkClient.users.getUserList();
		return c.json(users);
	})
	// READ User by ID
	.get("/:id", verifyRequest(), async (c) => {
		const id = c.req.param("id");
		const user = await clerkClient.users.getUser(id);
		return c.json(user);
	})
	// UPDATE User
	.put("/:id", verifyRequest(), async (c) => {
		const id = c.req.param("id");
		const { name, role } = await c.req.json();
		const user = await clerkClient.users.updateUser(id, {
			username: name,
			publicMetadata: { role },
		});
		return c.json(user);
	})
	// DELETE User
	.delete("/:id", verifyRequest(), async (c) => {
		const id = c.req.param("id");
		await clerkClient.users.deleteUser(id);
		return c.text("User deleted successfully");
	});

export default app;
