import { clerkMiddleware } from "@hono/clerk-auth";
import { Hono } from "hono";
import { pinoLogger } from "hono-pino";
import { Resource } from "sst";
import {
	signInInvalidPasswordErrorResponse,
	signInInvalidUserErrorResponse,
} from "../utils/errorResponses";
import { clerkClient } from "../utils/serviceClients";

const expiresInSeconds = 60 * 60 * 24 * 7; // 1 week
const app = new Hono();
// protect all routes with clerk middleware
app
	.use(
		pinoLogger({
			pino: { level: "info" },
			contextKey: "authLogger" as const,
		}),
	)
	.use(
		"*",
		clerkMiddleware({
			secretKey: Resource.AppSecrets.clerkSecretKey,
			publishableKey: Resource.AppSecrets.clerkPublishableKey,
		}),
	)
	.post("/signin", async (c) => {
		const logger = c.get("authLogger");
		const { email, password } = await c.req.json();

		try {
			const user = await clerkClient.users.getUserList({
				emailAddress: [email],
			});
			if (!user.data.length) {
				return c.json(signInInvalidUserErrorResponse.statusText, 401);
			}

			try {
				const userId = user.data[0].id;
				const checkPasswordVerification =
					await clerkClient.users.verifyPassword({
						userId,
						password,
					});

				if (!checkPasswordVerification.verified) {
					return c.json(
						{ message: signInInvalidPasswordErrorResponse.statusText },
						401,
					);
				}

				const token = await clerkClient.signInTokens.createSignInToken({
					userId,
					expiresInSeconds,
				});
				return c.json(token);
			} catch (error) {
				return c.json(
					{ message: signInInvalidPasswordErrorResponse.statusText },
					401,
				);
			}
		} catch (error) {
			logger.error({ error }, "Error getting user");
			return c.json(signInInvalidUserErrorResponse);
		}
	})
	.post("/signout", async (c) => {
		const logger = c.get("authLogger");
		const { signInTokenId } = await c.req.json();
		try {
			await clerkClient.signInTokens.revokeSignInToken(signInTokenId);
			return c.text("Sign out successful");
		} catch (error) {
			logger.error({ error }, "Sign out failed");
			return c.json({ message: "Sign out failed" }, 401);
		}
	});

export default app;
