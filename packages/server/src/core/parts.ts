import { clerkMiddleware } from "@hono/clerk-auth";
import { zValidator } from "@hono/zod-validator";
import { Hono } from "hono";
import { pinoLogger } from "hono-pino";
import { Resource } from "sst";
import { PartsSchema } from "./schemas/parts-schema";
import { isValid, ulid } from "ulidx";
import { addNewPart, getSinglePartById } from "../models/parts";
import { docClient } from "../utils/serviceClients";
import { GetItemCommand, PutItemCommand } from "@aws-sdk/client-dynamodb";
import { AWSServiceErrors, logErrorAndThrow } from "../utils/errorResponses";
import { z } from "zod";

const partsInventory = new Hono()
	.use(
		pinoLogger({
			pino: { level: "info" },
			contextKey: "partsLogger" as const,
		}),
	)
	.use(
		clerkMiddleware({
			secretKey: Resource.AppSecrets.clerkSecretKey,
			publishableKey: Resource.AppSecrets.clerkPublishableKey,
		}),
	)
	.get(
		"/:id",
		zValidator(
			"param",
			z.object({
				id: z
					.string()
					.refine((val) => isValid(val), { message: "ID provided is invalid" }),
			}),
		),
		async (c) => {
			const logger = c.get("partsLogger");
			const { id } = c.req.valid("param");
			try {
				const getPartPayload = getSinglePartById(id);
				const response = await docClient.send(
					new GetItemCommand(getPartPayload),
				);
				const { Item } = response;
				if (!Item) {
					return c.json({ message: "Part not found" }, 404);
				}
				return c.json({
					message: "Part fetched successfully",
					data: Item,
				});
			} catch (error) {
				if (error instanceof AWSServiceErrors) {
					logErrorAndThrow(logger, error);
				} else {
					logger.error(error);
					return c.json(
						{
							message: "An error occurred while fetching this vehicle",
						},
						400,
					);
				}
			}
		},
	)
	.post("/", zValidator("json", PartsSchema), async (c) => {
		const logger = c.get("partsLogger");
		const partsPayload = c.req.valid("json");
		try {
			const id = ulid();
			const addPartPayload = addNewPart(partsPayload, id);

			await docClient.send(new PutItemCommand(addPartPayload));
			return c.json({
				message: `${partsPayload.name} successfully added to your inventory`,
			});
		} catch (error) {
			if (error instanceof AWSServiceErrors) {
				logErrorAndThrow(logger, error);
			} else {
				logger.error(error);
				return c.json(
					{
						message: "An error occurred while add this part.",
					},
					500,
				);
			}
		}
	});

export default partsInventory;
