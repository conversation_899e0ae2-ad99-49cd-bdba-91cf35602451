import {
	GetItemCommand,
	PutItemCommand,
	QueryCommand,
} from "@aws-sdk/client-dynamodb";
import { clerkMiddleware } from "@hono/clerk-auth";
import { Hono } from "hono";
import { Resource } from "sst";

import { pinoLogger } from "hono-pino";
import {
	addNewVehicleToStore,
	getAllVehicles,
	getSingleVehicleById,
	queryByPriceRange,
	queryVehiclesByMake,
} from "../models/vehicle";

import { ScanCommand } from "@aws-sdk/lib-dynamodb";
import { zValidator } from "@hono/zod-validator";
import { isValid, ulid } from "ulidx";
import { z } from "zod";
import { AWSServiceErrors, logErrorAndThrow } from "../utils/errorResponses";
import { docClient } from "../utils/serviceClients";
import {
	transformSingleVehicleResponse,
	transformVehiclesResponse,
} from "../utils/transformers";
import { NewVehicleSchema } from "./schemas/vehicle-schema";

const app = new Hono()
	.use(
		pinoLogger({
			pino: { level: "info" },
			contextKey: "dmscorelogger" as const,
		}),
	)
	.use(
		"*",
		clerkMiddleware({
			secretKey: Resource.AppSecrets.clerkSecretKey,
			publishableKey: Resource.AppSecrets.clerkPublishableKey,
		}),
	)
	.get(
		"/make/:name",
		zValidator(
			"query",
			z.object({
				limit: z.string().optional(),
				startPrice: z.string().optional(),
				endPrice: z.string().optional(),
			}),
		),
		async (c) => {
			const logger = c.get("dmscorelogger");
			const make = c.req.param("name");
			const { limit, startPrice, endPrice } = c.req.valid("query");
			try {
				if (!startPrice || !endPrice) {
					const payload = queryVehiclesByMake({
						make,
						limit: Number.parseInt(limit ?? "10", 10),
					});
					const response = await docClient.send(new QueryCommand(payload));
					const { Items, Count, ScannedCount } = response;
					const transformedResponse = transformVehiclesResponse({
						responseItem: Items,
						responseCount: Count,
						responseScannedCount: ScannedCount,
					});
					return c.json(transformedResponse);
				}
				const payload = queryByPriceRange({
					make,
					startPrice: Number.parseInt(startPrice, 10),
					endPrice: Number.parseInt(endPrice, 10),
					limit: Number.parseInt(limit ?? "10", 10),
				});
				const response = await docClient.send(new QueryCommand(payload));
				const { Items, Count, ScannedCount } = response;
				const transformedResponse = transformVehiclesResponse({
					responseItem: Items,
					responseCount: Count,
					responseScannedCount: ScannedCount,
				});
				return c.json(transformedResponse);
			} catch (error) {
				if (error instanceof AWSServiceErrors) {
					logErrorAndThrow(logger, error);
				} else {
					logger.error(error);
					return c.json(
						{
							message: "An error occurred while fetching vehicles",
						},
						500,
					);
				}
			}
		},
	)
	.get(
		"/:id",
		zValidator(
			"param",
			z.object({
				id: z
					.string()
					.refine((val) => isValid(val), { message: "ID provided is invalid" }),
			}),
		),
		async (c) => {
			const logger = c.get("dmscorelogger");
			const { id } = c.req.valid("param");
			try {
				const requestPayload = getSingleVehicleById(id);
				const response = await docClient.send(
					new GetItemCommand(requestPayload),
				);
				const { Item } = response;
				if (!Item) {
					return c.json({ message: "Vehicle not found" }, 404);
				}
				const vehicle = transformSingleVehicleResponse({ responseItem: Item });
				return c.json({
					message: "Vehicle fetched successfully",
					data: vehicle,
				});
			} catch (error) {
				if (error instanceof AWSServiceErrors) {
					logErrorAndThrow(logger, error);
				} else {
					logger.error(error);
					return c.json(
						{
							message: "An error occurred while fetching this vehicle",
						},
						400,
					);
				}
			}
		},
	)
	.get(
		"/",
		zValidator(
			"query",
			z.object({
				limit: z.number().default(10),
			}),
		),
		async (c) => {
			const logger = c.get("dmscorelogger");
			const { limit } = c.req.valid("query");
			try {
				const getAllPayload = getAllVehicles(limit);
				const response = await docClient.send(new ScanCommand(getAllPayload));
				const { Items, Count, ScannedCount } = response;
				// const transformedResponse = transformVehiclesResponse({
				// 	responseItem: Items,
				// 	responseCount: Count,
				// 	responseScannedCount: ScannedCount,
				// });
				return c.json({ Items, Count, ScannedCount });
			} catch (error) {
				if (error instanceof AWSServiceErrors) {
					logErrorAndThrow(logger, error);
				} else {
					logger.error(error);
					return c.json(
						{
							message: "An error occurred while fetching vehicles",
						},
						500,
					);
				}
			}
		},
	)
	.post("/", zValidator("json", NewVehicleSchema), async (c) => {
		const logger = c.get("dmscorelogger");
		const vehiclePayload = c.req.valid("json");
		try {
			const id = ulid();
			const requestPayload = addNewVehicleToStore(vehiclePayload, id);
			// logger.info({ requestPayload });
			// return c.json(requestPayload);
			await docClient.send(new PutItemCommand(requestPayload));
			return c.json({
				message: "Vehicle created successfully",
			});
		} catch (error) {
			if (error instanceof AWSServiceErrors) {
				logErrorAndThrow(logger, error);
			} else {
				logger.error(error);
				return c.json(
					{
						message: "An error occurred while creating this vehicle",
					},
					500,
				);
			}
		}
	});

export default app;
