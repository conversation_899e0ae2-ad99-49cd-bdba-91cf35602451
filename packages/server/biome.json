{"$schema": "https://biomejs.dev/schemas/1.9.4/schema.json", "vcs": {"enabled": true, "clientKind": "git", "useIgnoreFile": true}, "files": {"ignoreUnknown": false, "ignore": ["sst-env.d.ts"]}, "formatter": {"enabled": true, "indentStyle": "tab", "indentWidth": 2, "lineWidth": 80}, "organizeImports": {"enabled": true}, "linter": {"enabled": true, "rules": {"recommended": true}}, "javascript": {"formatter": {"quoteStyle": "double", "semicolons": "always"}}}