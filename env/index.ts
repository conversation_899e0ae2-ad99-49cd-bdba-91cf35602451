export const clerkSecret<PERSON><PERSON> = new sst.Secret("CLERK_SECRET_KEY");
export const clerkPublishableKey = new sst.Secret("CLERK_PUBLISHABLE_KEY");
export const clerkJwtAudience = new sst.Secret("CLERK_JWT_AUDIENCE");
export const clerkJwtKey = new sst.Secret("CLERK_JWT_KEY");

export const appSecrets = new sst.Linkable("AppSecrets", {
	properties: {
		clerkSecretKey: clerkSecretKey.value,
		clerkPublishableKey: clerkPublishableKey.value,
		clerkJwtAudience: clerkJwtAudience.value,
		clerkJwtKey: clerkJwtKey.value,
	},
});
