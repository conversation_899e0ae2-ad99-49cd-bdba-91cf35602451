/// <reference path="./.sst/platform/config.d.ts" />

export default $config({
	app(input) {
		return {
			name: "dms-core",
			removal: input?.stage === "production" ? "retain" : "remove",
			protect: ["production"].includes(input?.stage),
			home: "aws",
			providers: {
				aws: {
					profile:
						input.stage === "production" ? "dmscore-prod" : "dmscore-dev",
				},
			},
		};
	},
	async run() {
		const { appSecrets } = await import("./env/index");
		const { DMSStorage } = await import("./infra/storage");
		const { DMSCoreTable } = await import("./infra/database");
		await import("./infra/web");
		new sst.aws.Function("DMSCoreFunction", {
			url: true,
			handler: "packages/server/src/index.handler",
			link: [appSecrets, DMSCoreTable, DMSStorage],
			environment: {
				LOG_LEVEL: "info",
				DEBUG: "true",
			},
			logging: {
				format: "json",
			},
		});
	},
	console: {
		autodeploy: {
			target(event) {
				if (
					event.type === "branch" &&
					event.branch === "dev" &&
					event.action === "pushed"
				) {
					return { stage: "dev" };
				}
				if (
					event.type === "branch" &&
					event.branch === "main" &&
					event.action === "pushed"
				) {
					return { stage: "production" };
				}
			},
		},
	},
});
